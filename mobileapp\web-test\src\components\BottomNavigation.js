import React from 'react';

const BottomNavigation = ({ currentScreen, onScreenChange }) => {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: '🏠' },
    { id: 'portfolio', label: 'Portfolio', icon: '📊' },
    { id: 'purchase', label: 'Buy', icon: '🛒' },
    { id: 'commission', label: 'Commission', icon: '💰' },
    { id: 'profile', label: 'Profile', icon: '👤' },
  ];

  return (
    <div className="mobile-bottom-nav">
      {navItems.map((item) => (
        <div
          key={item.id}
          className={`nav-item ${currentScreen === item.id ? 'active' : ''}`}
          onClick={() => onScreenChange(item.id)}
        >
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>
            {item.icon}
          </div>
          <div>{item.label}</div>
        </div>
      ))}
    </div>
  );
};

export default BottomNavigation;