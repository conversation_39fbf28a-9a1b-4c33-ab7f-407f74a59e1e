import React, { useState, useEffect } from 'react';
import { dbService } from '../services/supabaseClient';

const DashboardScreen = ({ user }) => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [currentPhase, setCurrentPhase] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Get user data from database
      const userData = await dbService.getUserByEmail(user.email);
      
      if (userData) {
        // Load portfolio data
        const [sharePurchases, commissionBalance, phase] = await Promise.all([
          dbService.getUserSharePurchases(userData.id),
          dbService.getCommissionBalance(userData.id),
          dbService.getCurrentPhase(),
        ]);

        // Calculate portfolio summary
        const totalShares = sharePurchases.reduce((sum, purchase) => 
          sum + (purchase.shares_purchased || 0), 0);
        const totalInvested = sharePurchases.reduce((sum, purchase) => 
          sum + (purchase.amount_paid || 0), 0);

        setPortfolioData({
          totalShares,
          totalInvested,
          sharePurchases,
          commissionBalance,
          activePurchases: sharePurchases.filter(p => p.status === 'active').length,
        });
        setCurrentPhase(phase);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    return parseFloat(number || 0).toLocaleString('en-US');
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="screen-container">
      <div style={{ marginBottom: '16px' }}>
        <h2 style={{ margin: '0 0 8px 0', color: '#1F2937' }}>
          Good day, {user.email.split('@')[0]}!
        </h2>
        <p style={{ margin: 0, color: '#6B7280' }}>
          Welcome to your share purchase dashboard
        </p>
      </div>

      {/* Portfolio Summary */}
      <div className="portfolio-summary">
        <div className="portfolio-value">
          {formatCurrency(portfolioData?.totalInvested || 0)}
        </div>
        <div className="portfolio-change">
          Total Purchase Value
        </div>
        
        <div className="stats-grid">
          <div className="stat-item">
            <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
              {formatNumber(portfolioData?.totalShares || 0)}
            </div>
            <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
              Total Shares
            </div>
          </div>
          <div className="stat-item">
            <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
              {portfolioData?.activePurchases || 0}
            </div>
            <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
              Active Purchases
            </div>
          </div>
        </div>
      </div>

      {/* Commission Summary */}
      {portfolioData?.commissionBalance && (
        <div className="commission-balance">
          <div className="balance-card usdt-card">
            <div className="balance-type">USDT Balance</div>
            <div className="balance-amount">
              {formatCurrency(portfolioData.commissionBalance.usdt_balance || 0, 'USDT')}
            </div>
          </div>
          <div className="balance-card share-card">
            <div className="balance-type">Share Balance</div>
            <div className="balance-amount">
              {formatNumber(portfolioData.commissionBalance.share_balance || 0)}
            </div>
          </div>
        </div>
      )}

      {/* Current Phase Info */}
      {currentPhase && (
        <div className="card">
          <div className="card-header">
            Current Purchase Phase {currentPhase.phase_number}
          </div>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#059669' }}>
              {formatCurrency(currentPhase.share_price)} per share
            </div>
            <div style={{ fontSize: '14px', color: '#6B7280' }}>
              {formatNumber(currentPhase.total_shares - (currentPhase.shares_sold || 0))} shares available
            </div>
          </div>
          {currentPhase.description && (
            <p style={{ margin: '0 0 16px 0', color: '#6B7280' }}>
              {currentPhase.description}
            </p>
          )}
          <button className="btn btn-primary">
            Purchase Shares
          </button>
        </div>
      )}

      {/* Recent Activity */}
      {portfolioData?.sharePurchases && portfolioData.sharePurchases.length > 0 && (
        <div className="card">
          <div className="card-header">Recent Activity</div>
          {portfolioData.sharePurchases.slice(0, 3).map((purchase, index) => (
            <div key={index} style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              padding: '12px 0',
              borderBottom: index < 2 ? '1px solid #F3F4F6' : 'none'
            }}>
              <div>
                <div style={{ fontWeight: '500', color: '#1F2937' }}>
                  Phase {purchase.phase_number || 'N/A'} Purchase
                </div>
                <div style={{ fontSize: '12px', color: '#6B7280' }}>
                  {new Date(purchase.created_at).toLocaleDateString()}
                </div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontWeight: '500', color: '#1F2937' }}>
                  {formatNumber(purchase.shares_purchased)} shares
                </div>
                <div className={`status-${purchase.status === 'active' ? 'success' : 'warning'}`}>
                  {purchase.status}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {(!portfolioData?.sharePurchases || portfolioData.sharePurchases.length === 0) && (
        <div className="card" style={{ textAlign: 'center', padding: '40px 16px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📈</div>
          <h3 style={{ margin: '0 0 8px 0', color: '#1F2937' }}>
            Start Your Share Purchase Journey
          </h3>
          <p style={{ margin: '0 0 24px 0', color: '#6B7280' }}>
            Purchase your first gold shares to begin building your portfolio.
          </p>
          <button className="btn btn-primary">
            Buy Your First Shares
          </button>
        </div>
      )}
    </div>
  );
};

export default DashboardScreen;