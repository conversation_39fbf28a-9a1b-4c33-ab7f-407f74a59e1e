/**
 * Aureus Alliance Holdings Mobile App
 * Login Screen
 * 
 * Allows existing users to sign in with email/password
 * Compatible with existing Telegram bot user accounts
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {colors, spacing} from '../../constants/theme';

const LoginScreen = ({navigation}) => {
  const [emailOrUsername, setEmailOrUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const {signIn, isLoading, error, clearError} = useAuth();

  useEffect(() => {
    if (error) {
      setSnackbarMessage(error);
      setSnackbarVisible(true);
      clearError();
    }
  }, [error]);

  const handleLogin = async () => {
    if (!emailOrUsername.trim() || !password.trim()) {
      setSnackbarMessage('Please enter both email/username and password');
      setSnackbarVisible(true);
      return;
    }

    let loginEmail = emailOrUsername.trim().toLowerCase();
    
    // If input doesn't contain @, treat as username and get email from database
    if (!loginEmail.includes('@')) {
      try {
        const {supabase} = require('../../services/supabaseClient');
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('email')
          .eq('username', loginEmail)
          .single();
          
        if (profileError || !profile) {
          setSnackbarMessage('Username not found. Please check your username or use email instead.');
          setSnackbarVisible(true);
          return;
        }
        
        loginEmail = profile.email;
      } catch (error) {
        setSnackbarMessage('Error looking up username. Please try again.');
        setSnackbarVisible(true);
        return;
      }
    } else if (!isValidEmail(loginEmail)) {
      setSnackbarMessage('Please enter a valid email address');
      setSnackbarVisible(true);
      return;
    }

    const result = await signIn(loginEmail, password);
    
    if (result.success) {
      // Navigation will be handled by the auth state change
      console.log('✅ Login successful');
    } else {
      setSnackbarMessage(result.error || 'Login failed. Please try again.');
      setSnackbarVisible(true);
    }
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleSignUp = () => {
    navigation.navigate('Register');
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        
        {/* Header */}
        <View style={styles.header}>
          <Icon name="account-balance" size={60} color={colors.secondary} />
          <Title style={styles.title}>Welcome Back</Title>
          <Paragraph style={styles.subtitle}>
            Sign in to access your Aureus Alliance Holdings account
          </Paragraph>
        </View>

        {/* Login Form */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.form}>
              {/* Email or Username Input */}
              <TextInput
                label="Email Address or Username"
                value={emailOrUsername}
                onChangeText={setEmailOrUsername}
                mode="outlined"
                keyboardType="default"
                autoCapitalize="none"
                autoCorrect={false}
                autoComplete="username"
                left={<TextInput.Icon icon="account" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Password Input */}
              <TextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                autoComplete="password"
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Forgot Password Link */}
              <TouchableOpacity
                onPress={handleForgotPassword}
                style={styles.forgotPasswordContainer}>
                <Text style={styles.forgotPasswordText}>
                  Forgot your password?
                </Text>
              </TouchableOpacity>

              {/* Login Button */}
              <Button
                mode="contained"
                onPress={handleLogin}
                loading={isLoading}
                disabled={isLoading}
                style={styles.loginButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}>
                {isLoading ? 'Signing In...' : 'Sign In'}
              </Button>

              {/* Divider */}
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>OR</Text>
                <View style={styles.dividerLine} />
              </View>

              {/* Sign Up Link */}
              <View style={styles.signUpContainer}>
                <Text style={styles.signUpText}>Don't have an account? </Text>
                <TouchableOpacity onPress={handleSignUp}>
                  <Text style={styles.signUpLink}>Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Migration Notice */}
        <Card style={styles.migrationCard}>
          <Card.Content>
            <View style={styles.migrationContent}>
              <Icon name="info" size={24} color={colors.primary} />
              <View style={styles.migrationText}>
                <Text style={styles.migrationTitle}>
                  Existing Telegram Users
                </Text>
                <Text style={styles.migrationDescription}>
                  Use the same email address or username from your Telegram bot account to access your share purchases and commission data.
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Snackbar for errors */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: spacing.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  card: {
    elevation: 4,
    borderRadius: 12,
    marginBottom: spacing.lg,
  },
  form: {
    paddingVertical: spacing.md,
  },
  input: {
    marginBottom: spacing.md,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginBottom: spacing.lg,
  },
  forgotPasswordText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    borderRadius: 8,
    marginBottom: spacing.lg,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.border,
  },
  dividerText: {
    marginHorizontal: spacing.md,
    color: colors.textSecondary,
    fontSize: 14,
  },
  signUpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signUpText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  signUpLink: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  migrationCard: {
    elevation: 2,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  migrationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  migrationText: {
    flex: 1,
    marginLeft: spacing.md,
  },
  migrationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  migrationDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  snackbar: {
    backgroundColor: colors.error,
  },
});

export default LoginScreen;