import React, { useState } from 'react';
import { supabase, authService } from '../services/supabaseClient';

const LoginScreen = ({ onLogin }) => {
  const [emailOrUsername, setEmailOrUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isRegister, setIsRegister] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (isRegister) {
        // For registration, treat input as email
        const { data, error } = await supabase.auth.signUp({
          email: emailOrUsername,
          password,
        });
        
        if (error) throw error;
        
        if (data.user) {
          setError('Registration successful! Please check your email for verification.');
        }
      } else {
        // Use the new auth service that handles both email and username
        const result = await authService.signIn(emailOrUsername, password);
        
        if (result.success && result.data.user) {
          onLogin(result.data.user);
        } else {
          throw new Error(result.error || 'Login failed');
        }
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mobile-content">
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <div style={{ 
          width: '80px', 
          height: '80px', 
          background: '#FFD700', 
          borderRadius: '50%', 
          margin: '0 auto 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#1E3A8A'
        }}>
          AU
        </div>
        <h1 style={{ color: '#1E3A8A', marginBottom: '8px' }}>
          {isRegister ? 'Create Account' : 'Welcome Back'}
        </h1>
        <p style={{ color: '#6B7280' }}>
          {isRegister 
            ? 'Join Aureus Alliance Holdings' 
            : 'Sign in to access your gold share purchases'
          }
        </p>
      </div>

      {error && (
        <div className={error.includes('successful') ? 'success-message' : 'error-message'}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="form-container">
        <div className="form-group">
          <label className="form-label">
            {isRegister ? 'Email Address' : 'Email Address or Username'}
          </label>
          <input
            type={isRegister ? "email" : "text"}
            className="form-input"
            value={emailOrUsername}
            onChange={(e) => setEmailOrUsername(e.target.value)}
            placeholder={isRegister ? "Enter your email" : "Enter your email or username"}
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label">Password</label>
          <input
            type="password"
            className="form-input"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            required
          />
        </div>

        <button 
          type="submit" 
          className="btn btn-primary"
          disabled={loading}
        >
          {loading ? 'Please wait...' : (isRegister ? 'Create Account' : 'Sign In')}
        </button>

        <button 
          type="button" 
          className="btn btn-secondary"
          onClick={() => setIsRegister(!isRegister)}
        >
          {isRegister ? 'Already have an account? Sign In' : 'Need an account? Sign Up'}
        </button>
      </form>

      <div className="info-message" style={{ marginTop: '24px' }}>
        <strong>Testing Instructions:</strong><br/>
        • Use any email/password to test registration<br/>
        • Use existing email from your database to test login<br/>
        • This connects to your live Supabase database<br/>
        • All data shown is real production data
      </div>
    </div>
  );
};

export default LoginScreen;