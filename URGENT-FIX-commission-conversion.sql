-- 🚨 URGENT FIX: Commission Conversion Function Missing
-- The bot is calling process_commission_conversion with 6 parameters but function doesn't exist
-- Run this in Supabase Dashboard > SQL Editor

-- First, check what functions currently exist
SELECT 
    routine_name, 
    routine_type,
    data_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name LIKE '%commission_conversion%'
ORDER BY routine_name;

-- Drop any conflicting functions
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, BIGINT, TEXT);
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, UUID, TEXT);

-- Create the function the bot is calling (6 parameters for creating conversions)
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_user_id UUID,
    p_shares_requested DECIMAL(10,2),
    p_usdt_amount DECIMAL(10,2),
    p_share_price DECIMAL(10,2),
    p_phase_id INTEGER,
    p_phase_number INTEGER
) RETURNS JSON AS $$
DECLARE
    v_current_balance DECIMAL(10,2);
    v_conversion_id UUID;
    v_result JSON;
BEGIN
    -- Check if user has sufficient commission balance
    SELECT COALESCE(usdt_balance, 0) INTO v_current_balance
    FROM commission_balances
    WHERE user_id = p_user_id;
    
    -- Validate sufficient balance
    IF v_current_balance < p_usdt_amount THEN
        RAISE EXCEPTION 'insufficient_balance';
    END IF;
    
    -- Generate conversion ID
    v_conversion_id := gen_random_uuid();
    
    -- Start atomic transaction
    BEGIN
        -- Immediately deduct balance (secure escrow)
        UPDATE commission_balances
        SET 
            usdt_balance = usdt_balance - p_usdt_amount,
            escrowed_amount = COALESCE(escrowed_amount, 0) + p_usdt_amount,
            last_updated = NOW()
        WHERE user_id = p_user_id;
        
        -- Create pending commission conversion record
        INSERT INTO commission_conversions (
            id,
            user_id,
            shares_requested,
            usdt_amount,
            share_price,
            phase_id,
            phase_number,
            status,
            created_at,
            updated_at
        ) VALUES (
            v_conversion_id,
            p_user_id,
            p_shares_requested,
            p_usdt_amount,
            p_share_price,
            p_phase_id,
            p_phase_number,
            'pending',
            NOW(),
            NOW()
        );
        
        -- Return success with conversion ID
        v_result := json_build_object(
            'success', true,
            'conversion_id', v_conversion_id,
            'message', 'Commission conversion created successfully',
            'balance_deducted', p_usdt_amount,
            'remaining_balance', v_current_balance - p_usdt_amount
        );
        
        RETURN v_result;
        
    EXCEPTION 
        WHEN OTHERS THEN
            -- Let the exception bubble up for proper error handling
            RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, DECIMAL, DECIMAL, DECIMAL, INTEGER, INTEGER) TO service_role;

-- Test the function exists
SELECT 
    routine_name, 
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name = 'process_commission_conversion'
ORDER BY routine_name;
