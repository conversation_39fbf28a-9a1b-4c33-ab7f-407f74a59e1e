/**
 * Aureus Alliance Holdings Mobile App
 * Authentication Context Provider
 * 
 * Manages user authentication state across the entire application
 */

import React, {createContext, useContext, useEffect, useReducer} from 'react';
import {authService, supabase, dbService} from '../services/supabaseClient';
import {userMigrationService} from '../services/userMigrationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initial state
const initialState = {
  user: null,
  session: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_SESSION: 'SET_SESSION',
  SET_ERROR: 'SET_ERROR',
  SIGN_OUT: 'SIGN_OUT',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer function
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.SET_SESSION:
      return {
        ...state,
        session: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };

    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.SIGN_OUT:
      return {
        ...initialState,
        isLoading: false,
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({children}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  // Listen for auth changes
  useEffect(() => {
    const {data: {subscription}} = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event);
        
        if (event === 'SIGNED_IN' && session) {
          await handleSignIn(session);
        } else if (event === 'SIGNED_OUT') {
          handleSignOut();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  /**
   * Initialize authentication state
   */
  const initializeAuth = async () => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});

      const session = await authService.getCurrentSession();
      
      if (session) {
        await handleSignIn(session);
      } else {
        dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      }
    } catch (error) {
      console.error('❌ Auth initialization failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
    }
  };

  /**
   * Handle successful sign in
   */
  const handleSignIn = async (session) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_SESSION, payload: session});

      // Get user profile from database
      const userProfile = await dbService.getUserProfile(session.user.id);
      
      if (userProfile) {
        dispatch({type: AUTH_ACTIONS.SET_USER, payload: userProfile});
      } else {
        // Try to find user by email (for existing bot users)
        const existingUser = await dbService.getUserByEmail(session.user.email);
        
        if (existingUser) {
          // Link auth user to existing user record
          console.log('🔗 Linking mobile auth to existing user account');
          dispatch({type: AUTH_ACTIONS.SET_USER, payload: existingUser});
        } else {
          // New user - will need to complete profile
          dispatch({type: AUTH_ACTIONS.SET_USER, payload: session.user});
        }
      }
    } catch (error) {
      console.error('❌ Sign in handling failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
    }
  };

  /**
   * Handle sign out
   */
  const handleSignOut = () => {
    dispatch({type: AUTH_ACTIONS.SIGN_OUT});
    // Clear any cached data
    AsyncStorage.multiRemove(['userProfile', 'portfolioData', 'commissionData']);
  };

  /**
   * Sign in with email and password
   */
  const signIn = async (email, password) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.signInWithEmail(email, password);
      
      if (result.session) {
        await handleSignIn(result.session);
        return {success: true, data: result};
      } else {
        throw new Error('Sign in failed - no session returned');
      }
    } catch (error) {
      console.error('❌ Sign in failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
      return {success: false, error: error.message};
    }
  };

  /**
   * Sign up with email and password
   */
  const signUp = async (email, password, userData = {}) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.signUpWithEmail(email, password, userData);
      
      return {success: true, data: result};
    } catch (error) {
      console.error('❌ Sign up failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
      return {success: false, error: error.message};
    }
  };

  /**
   * Sign out
   */
  const signOut = async () => {
    try {
      await authService.signOut();
      handleSignOut();
      return {success: true};
    } catch (error) {
      console.error('❌ Sign out failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
      return {success: false, error: error.message};
    }
  };

  /**
   * Reset password
   */
  const resetPassword = async (email) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      await authService.resetPassword(email);
      
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      return {success: true};
    } catch (error) {
      console.error('❌ Password reset failed:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
      return {success: false, error: error.message};
    }
  };

  /**
   * Clear error
   */
  const clearError = () => {
    dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});
  };

  /**
   * Refresh user data
   */
  const refreshUser = async () => {
    try {
      if (state.session) {
        const userProfile = await dbService.getUserProfile(state.session.user.id);
        if (userProfile) {
          dispatch({type: AUTH_ACTIONS.SET_USER, payload: userProfile});
        }
      }
    } catch (error) {
      console.error('❌ User refresh failed:', error);
    }
  };

  const value = {
    // State
    user: state.user,
    session: state.session,
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    
    // Actions
    signIn,
    signUp,
    signOut,
    resetPassword,
    clearError,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
      return {
        ...state,
        session: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case AUTH_ACTIONS.SIGN_OUT:
      return {
        ...initialState,
        isLoading: false,
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({children}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for existing session on app start
    checkAuthState();

    // Listen for auth state changes
    const {data: {subscription}} = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        if (session) {
          dispatch({type: AUTH_ACTIONS.SET_SESSION, payload: session});
          dispatch({type: AUTH_ACTIONS.SET_USER, payload: session.user});
          
          // Store user session info for offline access
          await AsyncStorage.setItem('user_session', JSON.stringify({
            userId: session.user.id,
            email: session.user.email,
            lastLogin: new Date().toISOString(),
          }));
        } else {
          dispatch({type: AUTH_ACTIONS.SIGN_OUT});
          await AsyncStorage.removeItem('user_session');
        }
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const checkAuthState = async () => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      
      const {session} = await authService.getSession();
      
      if (session) {
        dispatch({type: AUTH_ACTIONS.SET_SESSION, payload: session});
        dispatch({type: AUTH_ACTIONS.SET_USER, payload: session.user});
      } else {
        dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: error.message});
    }
  };

  const signUp = async (email, password, userData = {}) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      // Use migration service to create mobile-only user
      // This ensures proper integration with existing database schema
      const result = await userMigrationService.createMobileOnlyUser(email, password, userData);

      if (result.success) {
        // User created successfully - they can now sign in
        return {success: true, data: result.data};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign up failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const signIn = async (email, password) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.signIn(email, password);
      
      if (result.success) {
        // Auth state will be updated by the onAuthStateChange listener
        return {success: true, data: result.data};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign in failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const signOut = async () => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      
      const result = await authService.signOut();
      
      if (result.success) {
        // Auth state will be updated by the onAuthStateChange listener
        return {success: true};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Sign out failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const resetPassword = async (email) => {
    try {
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: true});
      dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});

      const result = await authService.resetPassword(email);
      
      dispatch({type: AUTH_ACTIONS.SET_LOADING, payload: false});
      
      if (result.success) {
        return {success: true};
      } else {
        dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: result.error});
        return {success: false, error: result.error};
      }
    } catch (error) {
      const errorMessage = error.message || 'Password reset failed';
      dispatch({type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage});
      return {success: false, error: errorMessage};
    }
  };

  const clearError = () => {
    dispatch({type: AUTH_ACTIONS.CLEAR_ERROR});
  };

  // Context value
  const value = {
    // State
    user: state.user,
    session: state.session,
    isLoading: state.isLoading,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    
    // Actions
    signUp,
    signIn,
    signOut,
    resetPassword,
    clearError,
    checkAuthState,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
