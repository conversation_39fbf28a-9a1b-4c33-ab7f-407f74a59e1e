{"mcpServers": {"supabase": {"command": "node", "args": ["mcp-supabase-server.js"], "disabled": false, "autoApprove": ["supabase_query", "supabase_insert", "supabase_update", "supabase_delete", "supabase_list_tables", "supabase_describe_table"]}, "sqlite": {"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "./test.db"], "disabled": false, "autoApprove": ["query", "list_tables", "describe_table"]}}}