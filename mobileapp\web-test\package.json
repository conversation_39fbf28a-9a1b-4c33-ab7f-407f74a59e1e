{"name": "aureus-web-test", "version": "1.0.0", "description": "Web testing version of Aureus Alliance mobile app", "private": true, "dependencies": {"@supabase/supabase-js": "^2.38.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}