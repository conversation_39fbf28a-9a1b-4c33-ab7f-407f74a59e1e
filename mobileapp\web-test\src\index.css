body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0F172A;
  color: #E2E8F0;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Mobile-like container */
.mobile-container {
  max-width: 414px;
  margin: 0 auto;
  min-height: 100vh;
  background: #1E293B;
  box-shadow: 0 0 20px rgba(0,0,0,0.3);
  position: relative;
}

/* Mobile-like header */
.mobile-header {
  background: #0F172A;
  color: #FFD700;
  padding: 16px;
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  border-bottom: 1px solid #334155;
}

/* Mobile-like content */
.mobile-content {
  padding: 16px;
  min-height: calc(100vh - 120px);
}

/* Mobile-like bottom navigation */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 414px;
  max-width: 100%;
  background: #0F172A;
  border-top: 1px solid #334155;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.3);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 12px;
  color: #64748B;
}

.nav-item.active {
  color: #FFD700;
}

.nav-item:hover {
  color: #FFD700;
}

/* Form styles */
.form-container {
  max-width: 100%;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #E2E8F0;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #475569;
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
  background: #334155;
  color: #E2E8F0;
}

.form-input:focus {
  outline: none;
  border-color: #FFD700;
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  margin-bottom: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #0F172A;
  font-weight: bold;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
}

.btn-secondary {
  background: #475569;
  color: #E2E8F0;
  border: 1px solid #64748B;
}

.btn-secondary:hover {
  background: #64748B;
}

/* Card styles */
.card {
  background: #334155;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  border: 1px solid #475569;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #FFD700;
}

/* Status indicators */
.status-success {
  color: #10B981;
  background: #ECFDF5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-warning {
  color: #F59E0B;
  background: #FFFBEB;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-error {
  color: #EF4444;
  background: #FEF2F2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Loading spinner */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #475569;
  border-top: 4px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 414px) {
  .mobile-container {
    max-width: 100%;
    box-shadow: none;
  }
  
  .mobile-bottom-nav {
    width: 100%;
    left: 0;
    transform: none;
  }
}