body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #F8FAFC;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Mobile-like container */
.mobile-container {
  max-width: 414px;
  margin: 0 auto;
  min-height: 100vh;
  background: white;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
  position: relative;
}

/* Mobile-like header */
.mobile-header {
  background: #1E3A8A;
  color: white;
  padding: 16px;
  text-align: center;
  font-weight: bold;
  font-size: 18px;
}

/* Mobile-like content */
.mobile-content {
  padding: 16px;
  min-height: calc(100vh - 120px);
}

/* Mobile-like bottom navigation */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 414px;
  max-width: 100%;
  background: white;
  border-top: 1px solid #E5E7EB;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 12px;
  color: #6B7280;
}

.nav-item.active {
  color: #1E3A8A;
}

.nav-item:hover {
  color: #1E3A8A;
}

/* Form styles */
.form-container {
  max-width: 100%;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-size: 16px;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1E3A8A;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  margin-bottom: 8px;
}

.btn-primary {
  background: #1E3A8A;
  color: white;
}

.btn-primary:hover {
  background: #1E40AF;
}

.btn-secondary {
  background: #F3F4F6;
  color: #374151;
  border: 1px solid #D1D5DB;
}

.btn-secondary:hover {
  background: #E5E7EB;
}

/* Card styles */
.card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #1F2937;
}

/* Status indicators */
.status-success {
  color: #10B981;
  background: #ECFDF5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-warning {
  color: #F59E0B;
  background: #FFFBEB;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-error {
  color: #EF4444;
  background: #FEF2F2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Loading spinner */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #F3F4F6;
  border-top: 4px solid #1E3A8A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 414px) {
  .mobile-container {
    max-width: 100%;
    box-shadow: none;
  }
  
  .mobile-bottom-nav {
    width: 100%;
    left: 0;
    transform: none;
  }
}