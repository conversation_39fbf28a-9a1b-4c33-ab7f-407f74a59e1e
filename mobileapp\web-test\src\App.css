/* Additional app-specific styles */
.app-container {
  min-height: 100vh;
  background: #F8FAFC;
}

.screen-container {
  padding-bottom: 80px; /* Space for bottom navigation */
}

.error-message {
  background: #FEF2F2;
  color: #DC2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #FECACA;
}

.success-message {
  background: #ECFDF5;
  color: #059669;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #A7F3D0;
}

.info-message {
  background: #EFF6FF;
  color: #2563EB;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #BFDBFE;
}

/* Portfolio specific styles */
.portfolio-summary {
  background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.portfolio-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.portfolio-change {
  font-size: 14px;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #1F2937;
}

.stat-label {
  font-size: 12px;
  color: #6B7280;
  margin-top: 4px;
}

/* Commission specific styles */
.commission-balance {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.balance-card {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  text-align: center;
}

.balance-type {
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 8px;
}

.balance-amount {
  font-size: 24px;
  font-weight: bold;
  color: #1F2937;
}

.usdt-card {
  border-left: 4px solid #26A17B;
}

.share-card {
  border-left: 4px solid #FFD700;
}

/* Purchase specific styles */
.phase-selector {
  margin-bottom: 20px;
}

.phase-option {
  background: white;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.phase-option:hover {
  border-color: #1E3A8A;
}

.phase-option.selected {
  border-color: #1E3A8A;
  background: #EFF6FF;
}

.phase-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 8px;
}

.phase-number {
  font-size: 18px;
  font-weight: bold;
  color: #1F2937;
}

.phase-price {
  font-size: 16px;
  font-weight: 600;
  color: #059669;
}

.calculation-summary {
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.calculation-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.calculation-total {
  border-top: 1px solid #E5E7EB;
  padding-top: 8px;
  margin-top: 8px;
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .commission-balance {
    grid-template-columns: 1fr;
  }
}