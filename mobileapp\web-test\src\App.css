/* Additional app-specific styles */
.app-container {
  min-height: 100vh;
  background: #0F172A;
}

/* Header layout styles */
.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #1E293B;
  border-bottom: 1px solid #475569;
  min-height: 80px;
}

.header-left {
  flex: 0 0 auto;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header-logo {
  height: 48px;
  width: auto;
  max-width: 120px;
  object-fit: contain;
  filter: brightness(1.1);
  transition: all 0.2s ease;
}

.header-logo:hover {
  filter: brightness(1.2);
  transform: scale(1.02);
}

.company-info {
  text-align: right;
  line-height: 1.2;
  max-width: 200px;
}

.company-name {
  font-size: 12px;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 3px;
  line-height: 1.1;
}

.company-location {
  font-size: 9px;
  color: #94A3B8;
  margin-bottom: 1px;
  line-height: 1.1;
}

.company-reg {
  font-size: 9px;
  color: #64748B;
  font-weight: 500;
  margin-top: 2px;
}

/* Responsive header adjustments */
@media (max-width: 414px) {
  .mobile-header {
    padding: 12px 16px;
    min-height: 85px;
  }
  
  .header-logo {
    height: 40px;
    max-width: 100px;
  }
  
  .company-info {
    max-width: 180px;
  }
  
  .company-name {
    font-size: 10px;
  }
  
  .company-location {
    font-size: 8px;
  }
  
  .company-reg {
    font-size: 8px;
  }
}

@media (max-width: 360px) {
  .mobile-header {
    padding: 10px 12px;
    min-height: 80px;
  }
  
  .header-logo {
    height: 36px;
    max-width: 90px;
  }
  
  .company-info {
    max-width: 160px;
  }
  
  .company-name {
    font-size: 9px;
  }
  
  .company-location {
    font-size: 7px;
  }
  
  .company-reg {
    font-size: 7px;
  }
}

.screen-container {
  padding-bottom: 80px; /* Space for bottom navigation */
}

.error-message {
  background: #7F1D1D;
  color: #FCA5A5;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #991B1B;
}

.success-message {
  background: #064E3B;
  color: #6EE7B7;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #047857;
}

.info-message {
  background: #1E3A8A;
  color: #BFDBFE;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #3B82F6;
}

/* Portfolio specific styles */
.portfolio-summary {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #0F172A;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.portfolio-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.portfolio-change {
  font-size: 14px;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #E2E8F0;
}

.stat-label {
  font-size: 12px;
  color: #94A3B8;
  margin-top: 4px;
}

/* Commission specific styles */
.commission-balance {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.balance-card {
  background: #334155;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.3);
  text-align: center;
  border: 1px solid #475569;
}

.balance-type {
  font-size: 14px;
  color: #94A3B8;
  margin-bottom: 8px;
}

.balance-amount {
  font-size: 24px;
  font-weight: bold;
  color: #E2E8F0;
}

.usdt-card {
  border-left: 4px solid #26A17B;
}

.share-card {
  border-left: 4px solid #FFD700;
}

/* Purchase specific styles */
.phase-selector {
  margin-bottom: 20px;
}

.phase-option {
  background: #475569;
  border: 2px solid #64748B;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.phase-option:hover {
  border-color: #FFD700;
}

.phase-option.selected {
  border-color: #FFD700;
  background: #334155;
  box-shadow: 0 0 0 1px #FFD700;
}

.phase-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #64748B;
}

.phase-option.disabled:hover {
  border-color: #64748B;
}

.phase-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 8px;
}

.phase-number {
  font-size: 18px;
  font-weight: bold;
  color: #E2E8F0;
}

.phase-price {
  font-size: 16px;
  font-weight: 600;
  color: #FFD700;
}

.calculation-summary {
  background: #1E293B;
  border: 1px solid #475569;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.calculation-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #E2E8F0;
}

.calculation-total {
  border-top: 1px solid #475569;
  padding-top: 8px;
  margin-top: 8px;
  font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .commission-balance {
    grid-template-columns: 1fr;
  }
}
/* Sli
de-in interface styles */
.slide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.slide-panel {
  background: #1E293B;
  border-radius: 20px 20px 0 0;
  padding: 24px;
  width: 100%;
  max-width: 414px;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  border: 1px solid #475569;
}

.slide-panel.open {
  transform: translateY(0);
}

.slide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #475569;
}

.slide-title {
  font-size: 20px;
  font-weight: bold;
  color: #FFD700;
}

.slide-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #94A3B8;
  cursor: pointer;
  padding: 4px;
}

.slide-close:hover {
  color: #E2E8F0;
}

/* Share quantity selector */
.quantity-selector {
  margin: 20px 0;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
}

.quantity-btn {
  background: #475569;
  border: 1px solid #64748B;
  color: #E2E8F0;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.quantity-btn:hover {
  background: #64748B;
  border-color: #FFD700;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  flex: 1;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  color: #FFD700;
  padding: 8px;
  background: #334155;
  border-radius: 8px;
  border: 1px solid #475569;
}

/* Payment method selection */
.payment-methods {
  margin: 20px 0;
}

.payment-method {
  background: #334155;
  border: 2px solid #475569;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-method:hover {
  border-color: #FFD700;
}

.payment-method.selected {
  border-color: #FFD700;
  background: #1E293B;
  box-shadow: 0 0 0 1px #FFD700;
}

.payment-method.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.payment-method.disabled:hover {
  border-color: #475569;
}

.payment-icon {
  font-size: 24px;
  width: 32px;
  text-align: center;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-size: 16px;
  font-weight: bold;
  color: #E2E8F0;
  margin-bottom: 4px;
}

.payment-description {
  font-size: 14px;
  color: #94A3B8;
}

.payment-badge {
  background: #FFD700;
  color: #0F172A;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
}

/* Commission balance display */
.commission-info {
  background: #1E3A8A;
  border: 1px solid #3B82F6;
  border-radius: 8px;
  padding: 12px;
  margin: 16px 0;
}

.commission-balance-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #BFDBFE;
}

.commission-amount {
  font-size: 18px;
  font-weight: bold;
  color: #FFD700;
}

/* Real-time calculation display */
.live-calculation {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #0F172A;
  padding: 16px;
  border-radius: 12px;
  margin: 16px 0;
  text-align: center;
}

.total-cost {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.cost-breakdown {
  font-size: 14px;
  opacity: 0.8;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Purchase flow progress indicator */
.purchase-progress {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  gap: 8px;
}

.progress-step {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #475569;
  transition: all 0.2s;
}

.progress-step.active {
  background: #FFD700;
  transform: scale(1.2);
}

.progress-step.completed {
  background: #10B981;
}

/* Responsive adjustments for slide panel */
@media (max-width: 414px) {
  .slide-panel {
    border-radius: 16px 16px 0 0;
    padding: 20px 16px;
  }
  
  .quantity-input-group {
    gap: 8px;
  }
  
  .quantity-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .quantity-display {
    font-size: 20px;
  }
}/* Pha
se Carousel Styles */
.phase-carousel {
  margin: 20px 0;
}

.phase-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.phase-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #475569;
  cursor: pointer;
  transition: all 0.2s;
}

.phase-indicator.active {
  background: #FFD700;
  transform: scale(1.2);
}

.phase-carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 16px;
}

.phase-card {
  background: #475569;
  border: 2px solid #64748B;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  min-height: 200px;
}

.phase-card.active-phase {
  border-color: #FFD700;
  background: #334155;
  box-shadow: 0 0 0 1px #FFD700;
}

.phase-card.inactive-phase {
  border-color: #64748B;
  background: #475569;
  opacity: 0.8;
}

.inactive-phase-message {
  background: #475569;
  color: #94A3B8;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  border: 1px solid #64748B;
}

.inactive-badge {
  background: #64748B !important;
  color: #E2E8F0 !important;
}

.phase-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
}

.phase-navigation-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.nav-arrow {
  background: #475569;
  border: 1px solid #64748B;
  color: #E2E8F0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-arrow:hover:not(:disabled) {
  background: #64748B;
  border-color: #FFD700;
}

.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.phase-counter {
  color: #E2E8F0;
  font-size: 14px;
  font-weight: 500;
}

.home-button {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #0F172A;
  border: none;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.home-button:hover {
  background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* Swipe animation */
.phase-card {
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.phase-card.swipe-left {
  transform: translateX(-100%);
}

.phase-card.swipe-right {
  transform: translateX(100%);
}

/* Mobile touch improvements */
.phase-carousel-container {
  touch-action: pan-y;
  user-select: none;
}

/* Responsive adjustments */
@media (max-width: 414px) {
  .phase-card {
    padding: 16px;
    min-height: 180px;
  }
  
  .nav-arrow {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
  
  .phase-indicators {
    gap: 6px;
  }
  
  .phase-indicator {
    width: 6px;
    height: 6px;
  }
}

/* Splash Screen Styles */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.splash-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  opacity: 0;
  transform: translateY(30px);
  transition: all 1.5s ease-out;
  z-index: 2;
}

.splash-content.show {
  opacity: 1;
  transform: translateY(0);
}

.logo-container {
  position: relative;
  margin-bottom: 40px;
  animation: logoAppear 2s ease-out forwards;
}

.logo-placeholder {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 
    0 0 30px rgba(255, 215, 0, 0.5),
    0 0 60px rgba(255, 215, 0, 0.3),
    0 0 90px rgba(255, 215, 0, 0.1);
  animation: pulse 2s ease-in-out infinite;
}

.logo-text {
  font-size: 48px;
  font-weight: bold;
  color: #0F172A;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Arial', sans-serif;
}

.logo-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow 3s ease-in-out infinite alternate;
}

.app-info {
  margin-bottom: 60px;
  animation: fadeInUp 2s ease-out 0.5s both;
}

.website {
  font-size: 24px;
  font-weight: 600;
  color: #FFD700;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  letter-spacing: 1px;
}

.version {
  font-size: 16px;
  color: #94A3B8;
  font-weight: 400;
}

.loading-container {
  width: 280px;
  animation: fadeInUp 2s ease-out 1s both;
}

.loading-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 16px;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
  border-radius: 2px;
  animation: loadingProgress 3s ease-out forwards;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.loading-text {
  font-size: 14px;
  color: #E2E8F0;
  text-align: center;
  opacity: 0.8;
}

.background-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.gold-gradient {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: rotate 20s linear infinite;
}

.shimmer-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.1) 50%, transparent 100%);
  animation: shimmer 3s ease-in-out infinite;
}

/* Animations */
@keyframes twinkle {
  0% { opacity: 0.3; transform: scale(1); }
  100% { opacity: 1; transform: scale(1.2); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes logoAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-180deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 
      0 0 30px rgba(255, 215, 0, 0.5),
      0 0 60px rgba(255, 215, 0, 0.3),
      0 0 90px rgba(255, 215, 0, 0.1);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 
      0 0 40px rgba(255, 215, 0, 0.7),
      0 0 80px rgba(255, 215, 0, 0.5),
      0 0 120px rgba(255, 215, 0, 0.3);
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingProgress {
  0% { width: 0%; }
  100% { width: 100%; }
}

@keyframes rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Mobile responsive splash screen */
@media (max-width: 414px) {
  .logo-placeholder {
    width: 100px;
    height: 100px;
  }
  
  .logo-text {
    font-size: 40px;
  }
  
  .website {
    font-size: 20px;
  }
  
  .version {
    font-size: 14px;
  }
  
  .loading-container {
    width: 240px;
  }
}