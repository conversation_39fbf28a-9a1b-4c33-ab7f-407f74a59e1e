/**
 * Aureus Alliance Holdings Mobile App
 * Dashboard Screen
 * 
 * Main dashboard showing portfolio overview, quick actions, and recent activity
 * Replaces the Telegram bot's main menu functionality
 */

import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Avatar,
  Chip,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, spacing, shadows} from '../../constants/theme';

const {width} = Dimensions.get('window');

const DashboardScreen = ({navigation}) => {
  const {user} = useAuth();
  const {
    portfolioSummary,
    currentPhase,
    isLoading,
    isRefreshing,
    refreshPortfolio,
    sharePurchases,
    commissionBalance,
  } = usePortfolio();

  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    setGreeting(getGreeting());
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const formatCurrency = (amount, currency = 'USD') => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    if (!number) return '0';
    return parseFloat(number).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  };

  const getRecentActivity = () => {
    if (!sharePurchases || sharePurchases.length === 0) return [];
    
    return sharePurchases
      .slice(0, 3)
      .map(purchase => ({
        id: purchase.id,
        type: 'share_purchase',
        title: `Share Purchase - Phase ${purchase.phase_number || 'N/A'}`,
        subtitle: `${formatNumber(purchase.shares_purchased)} shares`,
        amount: formatCurrency(purchase.amount_paid),
        status: purchase.status,
        date: purchase.created_at,
        icon: 'trending-up',
      }));
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'buy_shares':
        navigation.navigate('Purchase');
        break;
      case 'view_portfolio':
        navigation.navigate('Portfolio');
        break;
      case 'commissions':
        navigation.navigate('Commission');
        break;
      case 'profile':
        navigation.navigate('Profile');
        break;
      default:
        break;
    }
  };

  const onRefresh = async () => {
    await refreshPortfolio();
  };

  const recentActivity = getRecentActivity();

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContent}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          colors={[colors.primary]}
          tintColor={colors.primary}
        />
      }>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.userInfo}>
            <Avatar.Text
              size={50}
              label={user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
              style={styles.avatar}
            />
            <View style={styles.userDetails}>
              <Text style={styles.greeting}>{greeting}</Text>
              <Text style={styles.userName}>
                {user?.full_name || user?.username || 'User'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            onPress={() => navigation.navigate('Profile')}
            style={styles.profileButton}>
            <Icon name="settings" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Portfolio Summary Card */}
      <Card style={styles.portfolioCard}>
        <Card.Content>
          <View style={styles.portfolioHeader}>
            <Title style={styles.portfolioTitle}>Portfolio Overview</Title>
            <Chip
              icon="trending-up"
              style={styles.phaseChip}
              textStyle={styles.phaseChipText}>
              Phase {currentPhase?.phase_number || 'N/A'}
            </Chip>
          </View>

          <View style={styles.portfolioStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatNumber(portfolioSummary?.totalShares || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Shares</Text>
            </View>
            
            <View style={styles.statDivider} />
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatCurrency(portfolioSummary?.totalInvested || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Purchase Value</Text>
            </View>
          </View>

          {/* Commission Summary */}
          {commissionBalance && (
            <View style={styles.commissionSummary}>
              <View style={styles.commissionItem}>
                <Icon name="account-balance-wallet" size={20} color={colors.usdt} />
                <Text style={styles.commissionText}>
                  USDT: {formatCurrency(commissionBalance.usdt_balance || 0, '')}
                </Text>
              </View>
              <View style={styles.commissionItem}>
                <Icon name="stars" size={20} color={colors.secondary} />
                <Text style={styles.commissionText}>
                  Shares: {formatNumber(commissionBalance.share_balance || 0)}
                </Text>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.quickActionsCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Quick Actions</Title>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('buy_shares')}>
              <Surface style={styles.quickActionIcon}>
                <Icon name="add-shopping-cart" size={24} color={colors.primary} />
              </Surface>
              <Text style={styles.quickActionText}>Buy Shares</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('view_portfolio')}>
              <Surface style={styles.quickActionIcon}>
                <Icon name="pie-chart" size={24} color={colors.primary} />
              </Surface>
              <Text style={styles.quickActionText}>Portfolio</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('commissions')}>
              <Surface style={styles.quickActionIcon}>
                <Icon name="monetization-on" size={24} color={colors.primary} />
              </Surface>
              <Text style={styles.quickActionText}>Commissions</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickAction}
              onPress={() => handleQuickAction('profile')}>
              <Surface style={styles.quickActionIcon}>
                <Icon name="person" size={24} color={colors.primary} />
              </Surface>
              <Text style={styles.quickActionText}>Profile</Text>
            </TouchableOpacity>
          </View>
        </Card.Content>
      </Card>

      {/* Current Phase Info */}
      {currentPhase && (
        <Card style={styles.phaseCard}>
          <Card.Content>
            <View style={styles.phaseHeader}>
              <Icon name="timeline" size={24} color={colors.secondary} />
              <Title style={styles.phaseTitle}>
                Current Phase {currentPhase.phase_number}
              </Title>
            </View>
            <Paragraph style={styles.phaseDescription}>
              {currentPhase.description || 'Gold share purchase opportunity'}
            </Paragraph>
            <View style={styles.phaseDetails}>
              <Text style={styles.phasePrice}>
                Share Price: {formatCurrency(currentPhase.share_price)}
              </Text>
              <Text style={styles.phaseShares}>
                Available: {formatNumber(currentPhase.total_shares - (currentPhase.shares_sold || 0))} shares
              </Text>
            </View>
            <Button
              mode="contained"
              onPress={() => handleQuickAction('buy_shares')}
              style={styles.phaseButton}
              contentStyle={styles.buttonContent}>
              Purchase Shares
            </Button>
          </Card.Content>
        </Card>
      )}

      {/* Recent Activity */}
      {recentActivity.length > 0 && (
        <Card style={styles.activityCard}>
          <Card.Content>
            <View style={styles.activityHeader}>
              <Title style={styles.sectionTitle}>Recent Activity</Title>
              <TouchableOpacity onPress={() => navigation.navigate('Portfolio')}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>

            {recentActivity.map((activity) => (
              <View key={activity.id} style={styles.activityItem}>
                <Surface style={styles.activityIcon}>
                  <Icon name={activity.icon} size={20} color={colors.primary} />
                </Surface>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <Text style={styles.activitySubtitle}>{activity.subtitle}</Text>
                  <Text style={styles.activityDate}>
                    {new Date(activity.date).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.activityRight}>
                  <Text style={styles.activityAmount}>{activity.amount}</Text>
                  <Chip
                    style={[
                      styles.statusChip,
                      {backgroundColor: getStatusColor(activity.status)},
                    ]}
                    textStyle={styles.statusChipText}>
                    {activity.status}
                  </Chip>
                </View>
              </View>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Empty State */}
      {recentActivity.length === 0 && (
        <Card style={styles.emptyCard}>
          <Card.Content style={styles.emptyContent}>
            <Icon name="trending-up" size={60} color={colors.textLight} />
            <Title style={styles.emptyTitle}>Start Your Share Purchase Journey</Title>
            <Paragraph style={styles.emptyDescription}>
              Purchase your first gold shares to begin building your portfolio with Aureus Alliance Holdings.
            </Paragraph>
            <Button
              mode="contained"
              onPress={() => handleQuickAction('buy_shares')}
              style={styles.emptyButton}
              contentStyle={styles.buttonContent}>
              Buy Your First Shares
            </Button>
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
};

const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'active':
    case 'approved':
      return colors.success + '20';
    case 'pending':
      return colors.warning + '20';
    case 'cancelled':
    case 'rejected':
      return colors.error + '20';
    default:
      return colors.textLight + '20';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    paddingBottom: spacing.xl,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.secondary,
  },
  userDetails: {
    marginLeft: spacing.md,
  },
  greeting: {
    fontSize: 14,
    color: colors.white + 'CC',
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  profileButton: {
    padding: spacing.sm,
    backgroundColor: colors.white + '20',
    borderRadius: 20,
  },
  portfolioCard: {
    margin: spacing.lg,
    marginTop: -spacing.lg,
    elevation: 8,
    borderRadius: 16,
  },
  portfolioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  portfolioTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  phaseChip: {
    backgroundColor: colors.secondary + '20',
  },
  phaseChipText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  portfolioStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: colors.border,
  },
  commissionSummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  commissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commissionText: {
    marginLeft: spacing.xs,
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  quickActionsCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAction: {
    alignItems: 'center',
    flex: 1,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary + '10',
    elevation: 2,
  },
  quickActionText: {
    fontSize: 12,
    color: colors.text,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  phaseCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  phaseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  phaseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginLeft: spacing.sm,
  },
  phaseDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  phaseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  phasePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.success,
  },
  phaseShares: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  phaseButton: {
    borderRadius: 8,
  },
  buttonContent: {
    paddingVertical: spacing.xs,
  },
  activityCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  viewAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary + '10',
  },
  activityContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  activitySubtitle: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  activityDate: {
    fontSize: 12,
    color: colors.textLight,
  },
  activityRight: {
    alignItems: 'flex-end',
  },
  activityAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
  },
  statusChip: {
    marginTop: spacing.xs,
  },
  statusChipText: {
    fontSize: 10,
    fontWeight: '500',
  },
  emptyCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginVertical: spacing.md,
    paddingHorizontal: spacing.md,
  },
  emptyButton: {
    borderRadius: 8,
    marginTop: spacing.md,
  },
});

export default DashboardScreen;