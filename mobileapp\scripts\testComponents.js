#!/usr/bin/env node

/**
 * COMPONENT COMPILATION TEST
 * 
 * This script tests that all React Native components can be imported
 * and don't have syntax errors or missing dependencies.
 */

console.log('🧩 COMPONENT COMPILATION TEST');
console.log('=============================');
console.log('');

/**
 * Test component imports
 */
async function testComponentImports() {
  const components = [
    // Auth screens
    { name: 'LoginScreen', path: '../src/screens/auth/LoginScreen.js' },
    { name: 'RegisterScreen', path: '../src/screens/auth/RegisterScreen.js' },
    { name: 'ForgotPasswordScreen', path: '../src/screens/auth/ForgotPasswordScreen.js' },
    
    // Main screens
    { name: 'DashboardScreen', path: '../src/screens/main/DashboardScreen.js' },
    { name: 'PortfolioScreen', path: '../src/screens/main/PortfolioScreen.js' },
    { name: 'PurchaseScreen', path: '../src/screens/main/PurchaseScreen.js' },
    { name: 'CommissionScreen', path: '../src/screens/main/CommissionScreen.js' },
    { name: 'ProfileScreen', path: '../src/screens/main/ProfileScreen.js' },
    
    // Other components
    { name: 'SplashScreen', path: '../src/screens/SplashScreen.js' },
    
    // Services and contexts
    { name: 'SupabaseClient', path: '../src/services/supabaseClient.js' },
    { name: 'UserMigrationService', path: '../src/services/userMigrationService.js' },
    
    // Constants
    { name: 'Theme', path: '../src/constants/theme.js' },
  ];

  let passedComponents = 0;
  let totalComponents = components.length;

  console.log('🔍 Testing component imports...\n');

  for (const component of components) {
    try {
      // Try to require the component
      require(component.path);
      console.log(`✅ ${component.name} - imports successfully`);
      passedComponents++;
    } catch (error) {
      console.log(`❌ ${component.name} - import failed:`);
      console.log(`   Error: ${error.message}`);
      
      // Show specific error details for debugging
      if (error.code === 'MODULE_NOT_FOUND') {
        const missingModule = error.message.match(/'([^']+)'/);
        if (missingModule) {
          console.log(`   Missing dependency: ${missingModule[1]}`);
        }
      }
    }
  }

  console.log('');
  console.log('📊 COMPONENT TEST RESULTS');
  console.log('=========================');
  console.log(`✅ Passed: ${passedComponents}/${totalComponents} components`);
  console.log(`❌ Failed: ${totalComponents - passedComponents}/${totalComponents} components`);

  if (passedComponents === totalComponents) {
    console.log('');
    console.log('🎉 ALL COMPONENTS COMPILE SUCCESSFULLY!');
    console.log('');
    console.log('✅ The mobile app code is syntactically correct');
    console.log('✅ All dependencies are properly imported');
    console.log('✅ No missing modules or syntax errors');
    console.log('');
    console.log('🚀 Ready for Android device testing!');
  } else {
    console.log('');
    console.log('⚠️  SOME COMPONENTS HAVE ISSUES!');
    console.log('');
    console.log('Common fixes:');
    console.log('- Install missing dependencies: npm install');
    console.log('- Check import paths and file names');
    console.log('- Verify React Native compatibility');
  }

  return passedComponents === totalComponents;
}

/**
 * Test package.json scripts
 */
function testPackageScripts() {
  console.log('📦 Testing package.json scripts...\n');
  
  try {
    const packageJson = require('../package.json');
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = ['start', 'android', 'ios'];
    let foundScripts = 0;
    
    for (const script of requiredScripts) {
      if (scripts[script]) {
        console.log(`✅ ${script} script found`);
        foundScripts++;
      } else {
        console.log(`❌ ${script} script missing`);
      }
    }
    
    console.log(`\n📊 Scripts: ${foundScripts}/${requiredScripts.length} found`);
    return foundScripts === requiredScripts.length;
  } catch (error) {
    console.log('❌ Failed to read package.json');
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting component compilation tests...\n');
  
  const componentTest = await testComponentImports();
  console.log('');
  const scriptTest = testPackageScripts();
  
  if (componentTest && scriptTest) {
    console.log('\n🎉 ALL TESTS PASSED! Mobile app is ready for device testing.');
    console.log('\nNext steps:');
    console.log('1. Set up Android development environment');
    console.log('2. Run: npx react-native run-android');
    console.log('3. Test on physical device or emulator');
  } else {
    console.log('\n⚠️  Some tests failed. Fix issues before proceeding.');
  }
  
  return componentTest && scriptTest;
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Component test failed:', error);
  process.exit(1);
});