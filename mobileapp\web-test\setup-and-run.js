#!/usr/bin/env node

/**
 * WEB TEST SETUP AND RUN SCRIPT
 * 
 * This script sets up and runs the web version of the mobile app for testing
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🌐 AUREUS ALLIANCE WEB TEST SETUP');
console.log('=================================');
console.log('');

/**
 * Install dependencies
 */
function installDependencies() {
  console.log('📦 Installing web dependencies...');
  
  try {
    execSync('npm install', { 
      stdio: 'inherit',
      cwd: __dirname
    });
    console.log('✅ Dependencies installed successfully');
    return true;
  } catch (error) {
    console.log('❌ Failed to install dependencies');
    return false;
  }
}

/**
 * Start the web server
 */
function startWebServer() {
  console.log('🚀 Starting web development server...');
  console.log('');
  console.log('🌐 The web test will open at: http://localhost:3000');
  console.log('📱 It will look like a mobile app in your browser');
  console.log('');
  console.log('🧪 TESTING INSTRUCTIONS:');
  console.log('========================');
  console.log('1. Test login with existing email from your database');
  console.log('2. Test registration with a new email');
  console.log('3. Navigate through all screens using bottom navigation');
  console.log('4. Verify portfolio data loads correctly');
  console.log('5. Test commission tracking and referral links');
  console.log('6. Check that all data matches your Telegram bot data');
  console.log('');
  console.log('⚠️  IMPORTANT: This connects to your LIVE production database!');
  console.log('');
  
  try {
    execSync('npm start', { 
      stdio: 'inherit',
      cwd: __dirname
    });
  } catch (error) {
    console.log('❌ Failed to start web server');
    return false;
  }
}

/**
 * Main setup function
 */
async function main() {
  console.log('Setting up web test environment...\n');
  
  // Check if we're in the right directory
  if (!fs.existsSync(path.join(__dirname, 'package.json'))) {
    console.log('❌ package.json not found. Make sure you\'re in the web-test directory.');
    process.exit(1);
  }
  
  // Install dependencies
  const installSuccess = installDependencies();
  if (!installSuccess) {
    console.log('❌ Setup failed. Please check the errors above.');
    process.exit(1);
  }
  
  console.log('');
  console.log('✅ Setup complete! Starting web server...');
  console.log('');
  
  // Start the server
  startWebServer();
}

// Run the setup
main().catch(error => {
  console.error('💥 Setup failed:', error);
  process.exit(1);
});