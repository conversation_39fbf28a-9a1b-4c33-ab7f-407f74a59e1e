#!/usr/bin/env node

/**
 * MOBILE APP INTEGRATION TEST
 * 
 * This script tests the mobile app's integration with the live Supabase database
 * to ensure all critical functionality works correctly before production deployment.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Use the same configuration as the mobile app
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI';

console.log('🧪 MOBILE APP INTEGRATION TEST');
console.log('==============================');
console.log(`📊 Database URL: ${SUPABASE_URL}`);
console.log(`🔑 Using anon key: ${SUPABASE_ANON_KEY.substring(0, 20)}...`);
console.log('');

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Test database connection
 */
async function testDatabaseConnection() {
  console.log('🔗 Testing database connection...');
  
  try {
    // Test basic connection by querying a system table
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error && error.code !== 'PGRST116') {
      throw error;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Test user authentication flow
 */
async function testAuthenticationFlow() {
  console.log('🔐 Testing authentication flow...');
  
  try {
    // Test getting current session (should be null for unauthenticated)
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Authentication flow working (no active session as expected)');
    return true;
  } catch (error) {
    console.error('❌ Authentication flow failed:', error.message);
    return false;
  }
}

/**
 * Test reading user data (should work with RLS)
 */
async function testUserDataAccess() {
  console.log('👥 Testing user data access...');
  
  try {
    // Test reading users table (should respect RLS)
    const { data, error } = await supabase
      .from('users')
      .select('id, username, email, created_at')
      .limit(5);
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ User data access working (found ${data?.length || 0} users)`);
    return true;
  } catch (error) {
    console.error('❌ User data access failed:', error.message);
    return false;
  }
}

/**
 * Test reading investment phases
 */
async function testInvestmentPhases() {
  console.log('📈 Testing investment phases access...');
  
  try {
    const { data, error } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true);
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ Investment phases access working (found ${data?.length || 0} active phases)`);
    
    if (data && data.length > 0) {
      const currentPhase = data[0];
      console.log(`   📊 Current phase: ${currentPhase.phase_number} - ${currentPhase.share_price} USD per share`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Investment phases access failed:', error.message);
    return false;
  }
}

/**
 * Test reading company wallets
 */
async function testCompanyWallets() {
  console.log('💳 Testing company wallets access...');
  
  try {
    const { data, error } = await supabase
      .from('company_wallets')
      .select('*')
      .eq('is_active', true);
    
    if (error) {
      throw error;
    }
    
    console.log(`✅ Company wallets access working (found ${data?.length || 0} active wallets)`);
    
    if (data && data.length > 0) {
      data.forEach(wallet => {
        console.log(`   💰 ${wallet.network}: ${wallet.wallet_address?.substring(0, 20)}...`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Company wallets access failed:', error.message);
    return false;
  }
}

/**
 * Test reading share purchases (should be empty without auth)
 */
async function testSharePurchasesAccess() {
  console.log('🛒 Testing share purchases access...');
  
  try {
    const { data, error } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .limit(5);
    
    // This should either return empty data (due to RLS) or public data
    if (error && error.code !== 'PGRST116') {
      throw error;
    }
    
    console.log(`✅ Share purchases access working (RLS properly restricting data)`);
    return true;
  } catch (error) {
    console.error('❌ Share purchases access failed:', error.message);
    return false;
  }
}

/**
 * Test real-time subscriptions
 */
async function testRealtimeSubscriptions() {
  console.log('⚡ Testing real-time subscriptions...');
  
  try {
    // Create a test subscription
    const subscription = supabase
      .channel('test-channel')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'investment_phases'
      }, (payload) => {
        console.log('📡 Real-time event received:', payload);
      })
      .subscribe();
    
    // Wait a moment then unsubscribe
    setTimeout(() => {
      supabase.removeChannel(subscription);
      console.log('✅ Real-time subscriptions working');
    }, 1000);
    
    return true;
  } catch (error) {
    console.error('❌ Real-time subscriptions failed:', error.message);
    return false;
  }
}

/**
 * Test mobile app specific functionality
 */
async function testMobileAppFeatures() {
  console.log('📱 Testing mobile app specific features...');
  
  try {
    // Test that we can access all tables the mobile app needs
    const tables = [
      'users',
      'telegram_users', 
      'investment_phases',
      'company_wallets',
      'aureus_share_purchases',
      'commission_balances',
      'commission_transactions'
    ];
    
    let successCount = 0;
    
    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (!error || error.code === 'PGRST116') {
          successCount++;
          console.log(`   ✅ ${table} - accessible`);
        } else {
          console.log(`   ❌ ${table} - error: ${error.message}`);
        }
      } catch (err) {
        console.log(`   ❌ ${table} - error: ${err.message}`);
      }
    }
    
    console.log(`✅ Mobile app features test: ${successCount}/${tables.length} tables accessible`);
    return successCount === tables.length;
  } catch (error) {
    console.error('❌ Mobile app features test failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting mobile app integration tests...\n');
  
  const tests = [
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Authentication Flow', fn: testAuthenticationFlow },
    { name: 'User Data Access', fn: testUserDataAccess },
    { name: 'Investment Phases', fn: testInvestmentPhases },
    { name: 'Company Wallets', fn: testCompanyWallets },
    { name: 'Share Purchases Access', fn: testSharePurchasesAccess },
    { name: 'Real-time Subscriptions', fn: testRealtimeSubscriptions },
    { name: 'Mobile App Features', fn: testMobileAppFeatures },
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
      console.log(''); // Add spacing between tests
    } catch (error) {
      console.error(`❌ ${test.name} failed with exception:`, error.message);
      console.log('');
    }
  }
  
  // Final results
  console.log('📊 TEST RESULTS');
  console.log('===============');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('');
    console.log('🎉 ALL TESTS PASSED! Mobile app is ready for production deployment.');
    console.log('');
    console.log('Next steps:');
    console.log('1. Build production APK: cd android && ./gradlew assembleRelease');
    console.log('2. Test on physical devices');
    console.log('3. Submit to Google Play Store');
    console.log('4. Announce to users via Telegram bot');
  } else {
    console.log('');
    console.log('⚠️  SOME TESTS FAILED! Please fix issues before production deployment.');
    console.log('');
    console.log('Check the error messages above and ensure:');
    console.log('- Supabase credentials are correct');
    console.log('- Database is accessible');
    console.log('- RLS policies are properly configured');
    console.log('- All required tables exist');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});