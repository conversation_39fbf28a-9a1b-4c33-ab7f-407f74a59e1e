-- CRITICAL FIX: Create missing process_commission_conversion function for bot
-- The bot is calling process_commission_conversion with 6 parameters to CREATE conversions
-- but the existing function only accepts 3 parameters to APPROVE conversions
-- This creates the missing function that the bot expects

-- Drop existing function if it exists (to avoid conflicts)
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, B<PERSON>IN<PERSON>, TEXT);
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, UUID, TEXT);

-- Create the function that the bot is calling (6 parameters for creating conversions)
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_user_id UUID,
    p_shares_requested DECIMAL(10,2),
    p_usdt_amount DECIMAL(10,2),
    p_share_price DECIMAL(10,2),
    p_phase_id INTEGER,
    p_phase_number INTEGER
) RETURNS JSON AS $$
DECLARE
    v_current_balance DECIMAL(10,2);
    v_conversion_id UUID;
    v_result JSON;
BEGIN
    -- Check if user has sufficient commission balance
    SELECT COALESCE(usdt_balance, 0) INTO v_current_balance
    FROM commission_balances
    WHERE user_id = p_user_id;
    
    -- Validate sufficient balance
    IF v_current_balance < p_usdt_amount THEN
        RAISE EXCEPTION 'insufficient_balance';
    END IF;
    
    -- Generate conversion ID
    v_conversion_id := gen_random_uuid();
    
    -- Start atomic transaction
    BEGIN
        -- Immediately deduct balance (secure escrow)
        UPDATE commission_balances
        SET 
            usdt_balance = usdt_balance - p_usdt_amount,
            escrowed_amount = COALESCE(escrowed_amount, 0) + p_usdt_amount,
            last_updated = NOW()
        WHERE user_id = p_user_id;
        
        -- Create pending commission conversion record
        INSERT INTO commission_conversions (
            id,
            user_id,
            shares_requested,
            usdt_amount,
            share_price,
            phase_id,
            phase_number,
            status,
            created_at,
            updated_at
        ) VALUES (
            v_conversion_id,
            p_user_id,
            p_shares_requested,
            p_usdt_amount,
            p_share_price,
            p_phase_id,
            p_phase_number,
            'pending',
            NOW(),
            NOW()
        );
        
        -- Return success with conversion ID
        v_result := json_build_object(
            'success', true,
            'conversion_id', v_conversion_id,
            'message', 'Commission conversion created successfully',
            'balance_deducted', p_usdt_amount,
            'remaining_balance', v_current_balance - p_usdt_amount
        );
        
        RETURN v_result;
        
    EXCEPTION 
        WHEN OTHERS THEN
            -- Let the exception bubble up for proper error handling
            RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Create the approval function (3 parameters for approving existing conversions)
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_conversion_id UUID,
    p_admin_id BIGINT,
    p_admin_username TEXT
) RETURNS VOID AS $$
DECLARE
    v_conversion RECORD;
    v_user RECORD;
    v_phase RECORD;
    v_referral RECORD;
    v_sponsor_usdt DECIMAL(10,2) := 0;
    v_sponsor_shares DECIMAL(10,2) := 0;
    v_purchase_id UUID;
BEGIN
    -- Get conversion details
    SELECT * INTO v_conversion
    FROM commission_conversions
    WHERE id = p_conversion_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Conversion not found or already processed';
    END IF;
    
    -- Get user details
    SELECT * INTO v_user
    FROM users
    WHERE id = v_conversion.user_id;
    
    -- Get phase details
    SELECT * INTO v_phase
    FROM investment_phases
    WHERE id = v_conversion.phase_id;
    
    -- Start transaction
    BEGIN
        -- Update conversion status to approved
        UPDATE commission_conversions
        SET 
            status = 'approved',
            approved_by_admin_id = p_admin_id,
            approved_at = NOW(),
            updated_at = NOW()
        WHERE id = p_conversion_id;
        
        -- Release escrowed amount (already deducted during creation)
        UPDATE commission_balances
        SET 
            escrowed_amount = GREATEST(0, escrowed_amount - v_conversion.usdt_amount),
            last_updated = NOW()
        WHERE user_id = v_conversion.user_id;
        
        -- Generate purchase ID
        v_purchase_id := gen_random_uuid();
        
        -- Create share purchase record with ACTIVE status
        INSERT INTO aureus_share_purchases (
            id,
            user_id,
            phase_id,
            package_name,
            shares_purchased,
            total_amount,
            price_per_share,
            commission_used,
            remaining_payment,
            payment_method,
            status,
            purchase_date,
            created_at,
            updated_at
        ) VALUES (
            v_purchase_id,
            v_conversion.user_id,
            v_conversion.phase_id,
            CONCAT(v_phase.phase_name, ' - Commission Conversion'),
            v_conversion.shares_requested,
            v_conversion.usdt_amount,
            v_conversion.share_price,
            v_conversion.usdt_amount,
            0,
            'Commission Conversion',
            'active',
            NOW(),
            NOW(),
            NOW()
        );
        
        -- Update phase shares sold count
        UPDATE investment_phases
        SET 
            shares_sold = shares_sold + v_conversion.shares_requested,
            updated_at = NOW()
        WHERE id = v_conversion.phase_id;
        
        -- Check for referral and generate commission if applicable
        SELECT * INTO v_referral
        FROM referrals
        WHERE referred_id = v_conversion.user_id;
        
        IF FOUND THEN
            -- Calculate 15% commission for referrer
            v_sponsor_usdt := v_conversion.usdt_amount * 0.15;
            v_sponsor_shares := v_conversion.shares_requested * 0.15;
            
            -- Update referrer's commission balance
            INSERT INTO commission_balances (user_id, usdt_balance, share_balance, total_earned_usdt, total_earned_shares)
            VALUES (v_referral.referrer_id, v_sponsor_usdt, v_sponsor_shares, v_sponsor_usdt, v_sponsor_shares)
            ON CONFLICT (user_id) DO UPDATE SET
                usdt_balance = commission_balances.usdt_balance + v_sponsor_usdt,
                share_balance = commission_balances.share_balance + v_sponsor_shares,
                total_earned_usdt = commission_balances.total_earned_usdt + v_sponsor_usdt,
                total_earned_shares = commission_balances.total_earned_shares + v_sponsor_shares,
                last_updated = NOW();
            
            -- Create commission transaction record
            INSERT INTO commission_transactions (
                referrer_id,
                referred_id,
                share_purchase_id,
                usdt_commission,
                share_commission,
                commission_rate,
                share_purchase_amount,
                shares_purchased,
                status,
                payment_date,
                transaction_source
            ) VALUES (
                v_referral.referrer_id,
                v_conversion.user_id,
                v_purchase_id,
                v_sponsor_usdt,
                v_sponsor_shares,
                15.00,
                v_conversion.usdt_amount,
                v_conversion.shares_requested,
                'approved',
                NOW(),
                'commission_conversion'
            );
        END IF;
        
        -- Log admin action
        INSERT INTO admin_audit_logs (
            admin_id,
            admin_username,
            action,
            target_type,
            target_id,
            metadata,
            created_at
        ) VALUES (
            p_admin_id,
            p_admin_username,
            'commission_conversion_approved',
            'commission_conversion',
            p_conversion_id::TEXT,
            jsonb_build_object(
                'user_id', v_conversion.user_id,
                'shares_requested', v_conversion.shares_requested,
                'usdt_amount', v_conversion.usdt_amount,
                'phase_id', v_conversion.phase_id,
                'purchase_id', v_purchase_id,
                'sponsor_commission_usdt', v_sponsor_usdt,
                'sponsor_commission_shares', v_sponsor_shares
            ),
            NOW()
        );
        
    EXCEPTION WHEN OTHERS THEN
        -- Rollback will happen automatically
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, DECIMAL, DECIMAL, DECIMAL, INTEGER, INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, TEXT) TO service_role;
