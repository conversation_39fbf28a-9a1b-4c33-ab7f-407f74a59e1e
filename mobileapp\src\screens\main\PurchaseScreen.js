/**
 * Aureus Alliance Holdings Mobile App
 * Purchase Screen
 * 
 * Allows users to purchase gold shares from available investment phases
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  TextInput,
  Chip,
  Surface,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {useAuth} from '../../context/AuthContext';
import {colors, spacing} from '../../constants/theme';

const PurchaseScreen = ({navigation}) => {
  const {user} = useAuth();
  const {
    currentPhase,
    allPhases,
    companyWallets,
    createSharePurchase,
    isLoading,
    error,
  } = usePortfolio();

  const [selectedPhase, setSelectedPhase] = useState(null);
  const [shareAmount, setShareAmount] = useState('');
  const [calculatedCost, setCalculatedCost] = useState(0);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  useEffect(() => {
    if (currentPhase) {
      setSelectedPhase(currentPhase);
    }
  }, [currentPhase]);

  useEffect(() => {
    if (selectedPhase && shareAmount) {
      const shares = parseFloat(shareAmount) || 0;
      const cost = shares * (selectedPhase.share_price || 0);
      setCalculatedCost(cost);
    } else {
      setCalculatedCost(0);
    }
  }, [selectedPhase, shareAmount]);

  const formatCurrency = (amount, currency = 'USD') => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    if (!number) return '0';
    return parseFloat(number).toLocaleString('en-US');
  };

  const handlePhaseSelect = (phase) => {
    setSelectedPhase(phase);
  };

  const handleWalletSelect = (wallet) => {
    setSelectedWallet(wallet);
  };

  const validatePurchase = () => {
    if (!selectedPhase) {
      return 'Please select a purchase phase';
    }

    if (!shareAmount || parseFloat(shareAmount) <= 0) {
      return 'Please enter a valid number of shares';
    }

    const shares = parseFloat(shareAmount);
    const minShares = selectedPhase.min_shares || 1;
    const maxShares = selectedPhase.max_shares || 1000000;

    if (shares < minShares) {
      return `Minimum purchase is ${minShares} shares`;
    }

    if (shares > maxShares) {
      return `Maximum purchase is ${maxShares} shares`;
    }

    const availableShares = selectedPhase.total_shares - (selectedPhase.shares_sold || 0);
    if (shares > availableShares) {
      return `Only ${availableShares} shares available in this phase`;
    }

    if (!selectedWallet) {
      return 'Please select a payment wallet';
    }

    return null;
  };

  const handlePurchase = async () => {
    const validationError = validatePurchase();
    if (validationError) {
      setSnackbarMessage(validationError);
      setSnackbarVisible(true);
      return;
    }

    Alert.alert(
      'Confirm Purchase',
      `Purchase ${shareAmount} shares for ${formatCurrency(calculatedCost)}?\n\nYou will need to send payment to the selected wallet and upload proof.`,
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Confirm', onPress: processPurchase},
      ]
    );
  };

  const processPurchase = async () => {
    try {
      const purchaseData = {
        phase_id: selectedPhase.id,
        phase_number: selectedPhase.phase_number,
        shares_purchased: parseFloat(shareAmount),
        share_price: selectedPhase.share_price,
        amount_paid: calculatedCost,
        payment_wallet_id: selectedWallet.id,
        status: 'pending',
        payment_method: 'crypto',
      };

      const result = await createSharePurchase(purchaseData);

      if (result.success) {
        setSnackbarMessage('Purchase request submitted! Please upload payment proof.');
        setSnackbarVisible(true);
        
        // Navigate to payment proof upload
        setTimeout(() => {
          navigation.navigate('Portfolio');
        }, 2000);
      } else {
        setSnackbarMessage(result.error || 'Purchase failed. Please try again.');
        setSnackbarVisible(true);
      }
    } catch (error) {
      setSnackbarMessage('Purchase failed. Please try again.');
      setSnackbarVisible(true);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Phase Selection */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Select Purchase Phase</Title>
          
          {currentPhase && (
            <TouchableOpacity
              style={[
                styles.phaseOption,
                selectedPhase?.id === currentPhase.id && styles.selectedPhase,
              ]}
              onPress={() => handlePhaseSelect(currentPhase)}>
              <View style={styles.phaseHeader}>
                <Chip
                  icon="star"
                  style={styles.currentPhaseChip}
                  textStyle={styles.currentPhaseText}>
                  Current Phase
                </Chip>
                <Text style={styles.phaseNumber}>Phase {currentPhase.phase_number}</Text>
              </View>
              <Text style={styles.phasePrice}>
                {formatCurrency(currentPhase.share_price)} per share
              </Text>
              <Text style={styles.phaseAvailable}>
                {formatNumber(currentPhase.total_shares - (currentPhase.shares_sold || 0))} shares available
              </Text>
              {currentPhase.description && (
                <Text style={styles.phaseDescription}>{currentPhase.description}</Text>
              )}
            </TouchableOpacity>
          )}

          {allPhases && allPhases.length > 1 && (
            <View style={styles.otherPhases}>
              <Text style={styles.otherPhasesTitle}>Other Available Phases:</Text>
              {allPhases
                .filter(phase => phase.id !== currentPhase?.id && phase.is_active)
                .map(phase => (
                  <TouchableOpacity
                    key={phase.id}
                    style={[
                      styles.phaseOption,
                      selectedPhase?.id === phase.id && styles.selectedPhase,
                    ]}
                    onPress={() => handlePhaseSelect(phase)}>
                    <View style={styles.phaseHeader}>
                      <Text style={styles.phaseNumber}>Phase {phase.phase_number}</Text>
                    </View>
                    <Text style={styles.phasePrice}>
                      {formatCurrency(phase.share_price)} per share
                    </Text>
                    <Text style={styles.phaseAvailable}>
                      {formatNumber(phase.total_shares - (phase.shares_sold || 0))} shares available
                    </Text>
                  </TouchableOpacity>
                ))}
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Share Amount Input */}
      {selectedPhase && (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Enter Share Amount</Title>
            
            <TextInput
              label="Number of Shares"
              value={shareAmount}
              onChangeText={setShareAmount}
              mode="outlined"
              keyboardType="numeric"
              placeholder="Enter number of shares"
              left={<TextInput.Icon icon="trending-up" />}
              style={styles.input}
              theme={{colors: {primary: colors.primary}}}
            />

            {shareAmount && (
              <Surface style={styles.calculationSurface}>
                <View style={styles.calculationRow}>
                  <Text style={styles.calculationLabel}>Shares:</Text>
                  <Text style={styles.calculationValue}>{formatNumber(shareAmount)}</Text>
                </View>
                <View style={styles.calculationRow}>
                  <Text style={styles.calculationLabel}>Price per share:</Text>
                  <Text style={styles.calculationValue}>
                    {formatCurrency(selectedPhase.share_price)}
                  </Text>
                </View>
                <View style={[styles.calculationRow, styles.totalRow]}>
                  <Text style={styles.totalLabel}>Total Cost:</Text>
                  <Text style={styles.totalValue}>{formatCurrency(calculatedCost)}</Text>
                </View>
              </Surface>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Payment Wallet Selection */}
      {calculatedCost > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Select Payment Wallet</Title>
            
            {companyWallets && companyWallets.length > 0 ? (
              companyWallets.map(wallet => (
                <TouchableOpacity
                  key={wallet.id}
                  style={[
                    styles.walletOption,
                    selectedWallet?.id === wallet.id && styles.selectedWallet,
                  ]}
                  onPress={() => handleWalletSelect(wallet)}>
                  <View style={styles.walletHeader}>
                    <Icon name="account-balance-wallet" size={24} color={colors.primary} />
                    <Text style={styles.walletNetwork}>{wallet.network}</Text>
                  </View>
                  <Text style={styles.walletAddress}>{wallet.wallet_address}</Text>
                  {wallet.qr_code_url && (
                    <Text style={styles.walletQr}>QR Code Available</Text>
                  )}
                </TouchableOpacity>
              ))
            ) : (
              <Text style={styles.noWalletsText}>
                No payment wallets available. Please contact support.
              </Text>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Purchase Button */}
      {calculatedCost > 0 && selectedWallet && (
        <Card style={styles.card}>
          <Card.Content>
            <Button
              mode="contained"
              onPress={handlePurchase}
              loading={isLoading}
              disabled={isLoading}
              style={styles.purchaseButton}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}>
              {isLoading ? 'Processing...' : `Purchase ${shareAmount} Shares`}
            </Button>
            
            <Text style={styles.purchaseNote}>
              After confirming, you'll need to send {formatCurrency(calculatedCost)} to the selected wallet and upload payment proof.
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  card: {
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  phaseOption: {
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    marginBottom: spacing.sm,
  },
  selectedPhase: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  phaseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  currentPhaseChip: {
    backgroundColor: colors.secondary + '20',
  },
  currentPhaseText: {
    color: colors.primary,
    fontWeight: 'bold',
    fontSize: 12,
  },
  phaseNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  phasePrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: spacing.xs,
  },
  phaseAvailable: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  phaseDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  otherPhases: {
    marginTop: spacing.lg,
  },
  otherPhasesTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.md,
  },
  input: {
    marginBottom: spacing.md,
  },
  calculationSurface: {
    padding: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.surface,
    elevation: 2,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  calculationLabel: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  calculationValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.sm,
    marginTop: spacing.sm,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  walletOption: {
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    marginBottom: spacing.sm,
  },
  selectedWallet: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  walletHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  walletNetwork: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginLeft: spacing.sm,
  },
  walletAddress: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: 'monospace',
    marginBottom: spacing.xs,
  },
  walletQr: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  noWalletsText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: spacing.lg,
  },
  purchaseButton: {
    borderRadius: 8,
    marginBottom: spacing.md,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  purchaseNote: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  snackbar: {
    backgroundColor: colors.error,
  },
});

export default PurchaseScreen;