import { createClient } from '@supabase/supabase-js';

// Use the same Supabase configuration as the mobile app
const SUPABASE_URL = 'https://fgubaqoftdeefcakejwu.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI';

console.log('🔗 Initializing Supabase client for web testing...');

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// Authentication service functions
export const authService = {
  // Sign in with email or username
  async signIn(emailOrUsername, password) {
    try {
      let email = emailOrUsername;
      
      // Check if input looks like a username (no @ symbol)
      if (!emailOrUsername.includes('@')) {
        // Look up email by username
        const {data: user, error: userError} = await supabase
          .from('users')
          .select('email')
          .eq('username', emailOrUsername)
          .single();
          
        if (userError || !user) {
          throw new Error('Username not found. Please check your username or use email instead.');
        }
        
        email = user.email;
      }

      const {data, error} = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return {success: true, data};
    } catch (error) {
      console.error('Sign in error:', error);
      return {success: false, error: error.message};
    }
  },

  // Sign out
  async signOut() {
    try {
      const {error} = await supabase.auth.signOut();
      if (error) throw error;
      return {success: true};
    } catch (error) {
      console.error('Sign out error:', error);
      return {success: false, error: error.message};
    }
  },

  // Get current session
  async getSession() {
    try {
      const {data: {session}, error} = await supabase.auth.getSession();
      if (error) throw error;
      return {success: true, session};
    } catch (error) {
      console.error('Get session error:', error);
      return {success: false, error: error.message};
    }
  },
};

// Database service functions (simplified for web testing)
export const dbService = {
  // Get user by email
  async getUserByEmail(email) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user by email:', error);
      return null;
    }
    return data;
  },

  // Get user by username
  async getUserByUsername(username) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting user by username:', error);
      return null;
    }
    return data;
  },

  // Get user share purchases
  async getUserSharePurchases(userId) {
    const { data, error } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting share purchases:', error);
      return [];
    }
    return data || [];
  },

  // Get commission balance
  async getCommissionBalance(userId) {
    const { data, error } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting commission balance:', error);
      return null;
    }
    return data;
  },

  // Get commission transactions
  async getCommissionTransactions(userId) {
    const { data, error } = await supabase
      .from('commission_transactions')
      .select(`
        *,
        users!commission_transactions_referred_id_fkey (
          username,
          full_name
        )
      `)
      .eq('referrer_id', userId)
      .order('payment_date', { ascending: false });

    if (error) {
      console.error('Error getting commission transactions:', error);
      return [];
    }
    return data || [];
  },

  // Get current investment phase
  async getCurrentPhase() {
    const { data, error } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting current phase:', error);
      return null;
    }
    return data;
  },

  // Get all investment phases
  async getAllPhases() {
    const { data, error } = await supabase
      .from('investment_phases')
      .select('*')
      .order('phase_number', { ascending: true });

    if (error) {
      console.error('Error getting all phases:', error);
      return [];
    }
    return data || [];
  },

  // Get company wallets
  async getCompanyWallets() {
    const { data, error } = await supabase
      .from('company_wallets')
      .select('*')
      .eq('is_active', true);

    if (error) {
      console.error('Error getting company wallets:', error);
      return [];
    }
    return data || [];
  },
};

console.log('✅ Supabase client initialized for web testing');