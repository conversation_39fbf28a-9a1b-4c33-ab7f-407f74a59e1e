#!/usr/bin/env node

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');
const { CallToolRequestSchema, ListToolsRequestSchema } = require('@modelcontextprotocol/sdk/types.js');
const { createClient } = require('@supabase/supabase-js');

require('dotenv').config();

class SupabaseMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'supabase-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'supabase_query',
            description: 'Execute a SELECT query on Supabase',
            inputSchema: {
              type: 'object',
              properties: {
                table: {
                  type: 'string',
                  description: 'Table name to query'
                },
                select: {
                  type: 'string',
                  description: 'Columns to select (default: *)',
                  default: '*'
                },
                filter: {
                  type: 'object',
                  description: 'Filter conditions',
                  properties: {
                    column: { type: 'string' },
                    operator: { type: 'string', enum: ['eq', 'neq', 'gt', 'gte', 'lt', 'lte', 'like', 'ilike'] },
                    value: { type: 'string' }
                  }
                },
                limit: {
                  type: 'number',
                  description: 'Limit number of results'
                }
              },
              required: ['table']
            }
          },
          {
            name: 'supabase_insert',
            description: 'Insert data into Supabase table',
            inputSchema: {
              type: 'object',
              properties: {
                table: {
                  type: 'string',
                  description: 'Table name'
                },
                data: {
                  type: 'object',
                  description: 'Data to insert'
                }
              },
              required: ['table', 'data']
            }
          },
          {
            name: 'supabase_update',
            description: 'Update data in Supabase table',
            inputSchema: {
              type: 'object',
              properties: {
                table: {
                  type: 'string',
                  description: 'Table name'
                },
                data: {
                  type: 'object',
                  description: 'Data to update'
                },
                filter: {
                  type: 'object',
                  description: 'Filter conditions',
                  properties: {
                    column: { type: 'string' },
                    operator: { type: 'string', enum: ['eq', 'neq', 'gt', 'gte', 'lt', 'lte'] },
                    value: { type: 'string' }
                  },
                  required: ['column', 'operator', 'value']
                }
              },
              required: ['table', 'data', 'filter']
            }
          },
          {
            name: 'supabase_delete',
            description: 'Delete data from Supabase table',
            inputSchema: {
              type: 'object',
              properties: {
                table: {
                  type: 'string',
                  description: 'Table name'
                },
                filter: {
                  type: 'object',
                  description: 'Filter conditions',
                  properties: {
                    column: { type: 'string' },
                    operator: { type: 'string', enum: ['eq', 'neq', 'gt', 'gte', 'lt', 'lte'] },
                    value: { type: 'string' }
                  },
                  required: ['column', 'operator', 'value']
                }
              },
              required: ['table', 'filter']
            }
          },
          {
            name: 'supabase_list_tables',
            description: 'List all tables in the database',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          },
          {
            name: 'supabase_describe_table',
            description: 'Get table schema information',
            inputSchema: {
              type: 'object',
              properties: {
                table: {
                  type: 'string',
                  description: 'Table name'
                }
              },
              required: ['table']
            }
          }
        ]
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'supabase_query':
            return await this.handleQuery(args);
          case 'supabase_insert':
            return await this.handleInsert(args);
          case 'supabase_update':
            return await this.handleUpdate(args);
          case 'supabase_delete':
            return await this.handleDelete(args);
          case 'supabase_list_tables':
            return await this.handleListTables();
          case 'supabase_describe_table':
            return await this.handleDescribeTable(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`
            }
          ]
        };
      }
    });
  }

  async handleQuery(args) {
    const { table, select = '*', filter, limit } = args;
    
    let query = this.supabase.from(table).select(select);
    
    if (filter) {
      query = query[filter.operator](filter.column, filter.value);
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Query failed: ${error.message}`);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data, null, 2)
        }
      ]
    };
  }

  async handleInsert(args) {
    const { table, data } = args;
    
    const { data: result, error } = await this.supabase
      .from(table)
      .insert(data)
      .select();
    
    if (error) {
      throw new Error(`Insert failed: ${error.message}`);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Inserted ${result.length} row(s): ${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  }

  async handleUpdate(args) {
    const { table, data, filter } = args;
    
    const { data: result, error } = await this.supabase
      .from(table)
      .update(data)
      [filter.operator](filter.column, filter.value)
      .select();
    
    if (error) {
      throw new Error(`Update failed: ${error.message}`);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Updated ${result.length} row(s): ${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  }

  async handleDelete(args) {
    const { table, filter } = args;
    
    const { data: result, error } = await this.supabase
      .from(table)
      .delete()
      [filter.operator](filter.column, filter.value)
      .select();
    
    if (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Deleted ${result.length} row(s): ${JSON.stringify(result, null, 2)}`
        }
      ]
    };
  }

  async handleListTables() {
    const { data, error } = await this.supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (error) {
      throw new Error(`Failed to list tables: ${error.message}`);
    }
    
    const tables = data.map(row => row.table_name);
    
    return {
      content: [
        {
          type: 'text',
          text: `Tables: ${JSON.stringify(tables, null, 2)}`
        }
      ]
    };
  }

  async handleDescribeTable(args) {
    const { table } = args;
    
    const { data, error } = await this.supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', table)
      .eq('table_schema', 'public');
    
    if (error) {
      throw new Error(`Failed to describe table: ${error.message}`);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Table ${table} schema: ${JSON.stringify(data, null, 2)}`
        }
      ]
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Supabase MCP Server running on stdio');
  }
}

// Run the server
if (require.main === module) {
  const server = new SupabaseMCPServer();
  server.run().catch(console.error);
}

module.exports = SupabaseMCPServer;