import React, { useState, useEffect } from 'react';
import { dbService } from '../services/supabaseClient';

const CommissionScreen = ({ user }) => {
  const [commissionData, setCommissionData] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [referralLink, setReferralLink] = useState('');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawalAmount, setWithdrawalAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState('TRON');
  const [withdrawalLoading, setWithdrawalLoading] = useState(false);
  const [pendingWithdrawals, setPendingWithdrawals] = useState([]);

  useEffect(() => {
    loadCommissionData();
    loadReferralLink();
  }, [user]);

  const loadReferralLink = async () => {
    const link = await generateReferralLink();
    setReferralLink(link);
  };

  const loadCommissionData = async () => {
    try {
      setLoading(true);
      
      const userData = await dbService.getUserByEmail(user.email);
      
      if (userData) {
        const [commissionBalance, commissionTransactions, pendingWithdrawalsList] = await Promise.all([
          dbService.getCommissionBalance(userData.id),
          dbService.getCommissionTransactions(userData.id),
          dbService.getPendingWithdrawals(user.id),
        ]);

        setCommissionData(commissionBalance);
        setTransactions(commissionTransactions);
        setPendingWithdrawals(pendingWithdrawalsList);
      }
    } catch (error) {
      console.error('Error loading commission data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return `${currency} ${parseFloat(amount || 0).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    return parseFloat(number || 0).toLocaleString('en-US');
  };

  const generateReferralLink = async () => {
    try {
      // Get the user's username from the database
      const userData = await dbService.getUserByEmail(user.email);
      const username = userData?.username || user.email.split('@')[0];
      
      // Use the correct domain and format
      return `https://aureus.africa/${username}`;
    } catch (error) {
      console.error('Error generating referral link:', error);
      // Fallback to email prefix if database lookup fails
      const fallbackUsername = user.email.split('@')[0];
      return `https://aureus.africa/${fallbackUsername}`;
    }
  };

  const handleShareReferralLink = () => {
    const message = `🏆 Join me in owning shares of real South African gold mines!\n\n💰 AUREUS ALLIANCE HOLDINGS (PTY) LTD\n✅ CIPC-registered company\n✅ Operational gold mining sites\n✅ $20-$50 dividend per share projected\n\nStart with just $5 or invest up to $50,000+\n\n🔗 Join here: ${referralLink}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Join Aureus Alliance Holdings - Real Gold Mining Investment',
        text: message,
        url: referralLink,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(message).then(() => {
        alert('Referral message copied to clipboard!');
      }).catch(() => {
        // If clipboard fails, show the link in an alert
        alert(`Your referral link: ${referralLink}`);
      });
    }
  };

  const handleWithdrawClick = () => {
    // Check for pending withdrawals first
    if (pendingWithdrawals.length > 0) {
      const pending = pendingWithdrawals[0];
      alert(`⚠️ You have a pending withdrawal request of $${pending.amount} USDT. Please wait for admin approval before submitting a new request.`);
      return;
    }

    // Check minimum balance
    const availableBalance = parseFloat(commissionData?.usdt_balance || 0) - parseFloat(commissionData?.escrowed_amount || 0);
    if (availableBalance < 0.10) {
      alert(`❌ Insufficient balance. Minimum withdrawal is $0.10 USDT. Available: $${availableBalance.toFixed(2)} USDT`);
      return;
    }

    setShowWithdrawModal(true);
  };

  const handleWithdrawalSubmit = async () => {
    const amount = parseFloat(withdrawalAmount);
    
    // Validation
    if (isNaN(amount) || amount <= 0) {
      alert('❌ Please enter a valid amount');
      return;
    }

    if (amount < 0.10) {
      alert('❌ Minimum withdrawal amount is $0.10 USDT');
      return;
    }

    if (amount > 1000) {
      alert('❌ Maximum daily withdrawal is $1,000.00 USDT');
      return;
    }

    const availableBalance = parseFloat(commissionData?.usdt_balance || 0) - parseFloat(commissionData?.escrowed_amount || 0);
    if (amount > availableBalance) {
      alert(`❌ Insufficient balance. Available: $${availableBalance.toFixed(2)} USDT`);
      return;
    }

    if (!walletAddress.trim()) {
      alert('❌ Please enter your USDT wallet address');
      return;
    }

    // Basic wallet address validation
    if (walletAddress.length < 20) {
      alert('❌ Please enter a valid wallet address');
      return;
    }

    try {
      setWithdrawalLoading(true);

      const result = await dbService.createCommissionWithdrawal({
        user_id: user.id,
        amount: amount,
        wallet_address: walletAddress.trim(),
        network: selectedNetwork
      });

      if (result.success) {
        alert(`✅ Withdrawal request submitted successfully!\n\nAmount: $${amount.toFixed(2)} USDT\nFee: $2.00 USDT\nYou'll receive: $${(amount - 2).toFixed(2)} USDT\n\nAdmin will review your request within 24-48 hours.`);
        
        // Reset form and close modal
        setWithdrawalAmount('');
        setWalletAddress('');
        setShowWithdrawModal(false);
        
        // Reload data to show updated balance and pending withdrawal
        loadCommissionData();
      } else {
        alert(`❌ Withdrawal request failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Withdrawal error:', error);
      alert('❌ Error processing withdrawal request. Please try again.');
    } finally {
      setWithdrawalLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="screen-container">
      <h2 style={{ margin: '0 0 24px 0', color: '#1F2937' }}>
        Commission Dashboard
      </h2>

      {/* Commission Balances */}
      <div className="commission-balance">
        <div className="balance-card usdt-card">
          <div className="balance-type">💰 USDT Balance</div>
          <div className="balance-amount">
            {formatCurrency(commissionData?.usdt_balance || 0, 'USDT')}
          </div>
          <button 
            className="btn btn-secondary" 
            style={{ marginTop: '12px', fontSize: '12px' }}
            onClick={() => {
              console.log('🔥 WITHDRAW BUTTON CLICKED!');
              alert('Withdraw button works! Balance: ' + (commissionData?.usdt_balance || 0));
              handleWithdrawClick();
            }}
          >
            Withdraw
          </button>
        </div>
        
        <div className="balance-card share-card">
          <div className="balance-type">⭐ Share Balance</div>
          <div className="balance-amount">
            {formatNumber(commissionData?.share_balance || 0)}
          </div>
          <div style={{ fontSize: '10px', color: '#6B7280', marginTop: '8px' }}>
            Added to portfolio
          </div>
        </div>
      </div>

      {/* Total Earnings */}
      <div className="card">
        <div className="card-header">Total Earnings</div>
        
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-value" style={{ color: '#26A17B' }}>
              {formatCurrency(commissionData?.total_usdt_earned || 0, 'USDT')}
            </div>
            <div className="stat-label">Total USDT Earned</div>
          </div>
          <div className="stat-item">
            <div className="stat-value" style={{ color: '#FFD700' }}>
              {formatNumber(commissionData?.total_shares_earned || 0)}
            </div>
            <div className="stat-label">Total Shares Earned</div>
          </div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="card">
        <div className="card-header">🔗 Your Referral Link</div>
        
        <p style={{ margin: '0 0 16px 0', color: '#6B7280', fontSize: '14px' }}>
          Share your referral link and earn 15% USDT + 15% shares commission on every share purchase!
        </p>
        
        <div style={{ 
          background: '#F9FAFB',
          border: '1px solid #E5E7EB',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '16px',
          fontSize: '12px',
          fontFamily: 'monospace',
          wordBreak: 'break-all',
          color: '#1E3A8A'
        }}>
          {referralLink || 'Loading referral link...'}
        </div>
        
        <button 
          className="btn btn-primary"
          onClick={handleShareReferralLink}
        >
          📤 Share Referral Link
        </button>
      </div>

      {/* Commission History */}
      <div className="card">
        <div className="card-header">Commission History</div>
        
        {transactions && transactions.length > 0 ? (
          <div>
            {transactions.map((transaction, index) => (
              <div key={index} style={{ 
                padding: '16px 0',
                borderBottom: index < transactions.length - 1 ? '1px solid #F3F4F6' : 'none'
              }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'flex-start',
                  marginBottom: '8px'
                }}>
                  <div>
                    <div style={{ fontWeight: '500', color: '#1F2937' }}>
                      Referral Commission
                    </div>
                    <div style={{ fontSize: '12px', color: '#6B7280' }}>
                      {new Date(transaction.payment_date || transaction.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    {transaction.usdt_commission > 0 && (
                      <div style={{ 
                        background: '#ECFDF5',
                        color: '#059669',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '500',
                        marginBottom: '4px'
                      }}>
                        +{formatCurrency(transaction.usdt_commission, 'USDT')}
                      </div>
                    )}
                    {transaction.share_commission > 0 && (
                      <div style={{ 
                        background: '#FEF3C7',
                        color: '#D97706',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}>
                        +{formatNumber(transaction.share_commission)} shares
                      </div>
                    )}
                  </div>
                </div>
                
                {transaction.users && (
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#6B7280'
                  }}>
                    <span style={{ marginRight: '4px' }}>👤</span>
                    From: {transaction.users.full_name || transaction.users.username}
                  </div>
                )}
                
                <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                  Purchase: {formatCurrency(transaction.share_purchase_amount)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 16px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>💰</div>
            <h3 style={{ margin: '0 0 8px 0', color: '#1F2937' }}>
              No Commission History
            </h3>
            <p style={{ margin: '0 0 24px 0', color: '#6B7280' }}>
              Start referring friends to earn commission on their share purchases!
            </p>
            <button 
              className="btn btn-primary"
              onClick={handleShareReferralLink}
            >
              Share Referral Link
            </button>
          </div>
        )}
      </div>

      {/* Pending Withdrawals */}
      {pendingWithdrawals.length > 0 && (
        <div className="card">
          <div className="card-header">⏳ Pending Withdrawals</div>
          {pendingWithdrawals.map((withdrawal, index) => (
            <div key={index} style={{ 
              padding: '16px',
              background: '#FEF3C7',
              border: '1px solid #F59E0B',
              borderRadius: '8px',
              marginBottom: '12px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <div style={{ fontWeight: '500', color: '#92400E' }}>
                    ${withdrawal.amount} USDT
                  </div>
                  <div style={{ fontSize: '12px', color: '#78350F' }}>
                    Submitted: {new Date(withdrawal.created_at).toLocaleDateString()}
                  </div>
                  <div style={{ fontSize: '12px', color: '#78350F' }}>
                    Network: {withdrawal.network}
                  </div>
                </div>
                <div style={{ 
                  background: '#F59E0B',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '10px',
                  fontWeight: 'bold'
                }}>
                  PENDING
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Demo Notice */}
      <div className="info-message">
        <strong>Production System:</strong><br/>
        Withdrawal requests create real escrow and admin notifications. Funds are moved to escrow to prevent double-spending until admin approval.
      </div>

      {/* Withdrawal Modal */}
      {showWithdrawModal && (
        <div className="slide-overlay" onClick={() => setShowWithdrawModal(false)}>
          <div className="slide-panel open" onClick={(e) => e.stopPropagation()}>
            <div className="slide-header">
              <div className="slide-title">💸 USDT Withdrawal</div>
              <button className="slide-close" onClick={() => setShowWithdrawModal(false)}>×</button>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '14px', color: '#6B7280', marginBottom: '16px' }}>
                Available Balance: ${((parseFloat(commissionData?.usdt_balance || 0) - parseFloat(commissionData?.escrowed_amount || 0))).toFixed(2)} USDT
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#1F2937' }}>
                  Withdrawal Amount (USD)
                </label>
                <input
                  type="number"
                  value={withdrawalAmount}
                  onChange={(e) => setWithdrawalAmount(e.target.value)}
                  placeholder="Enter amount (min $0.10)"
                  min="0.10"
                  max="1000"
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #D1D5DB',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                />
                <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                  Processing fee: $2.00 USDT • You'll receive: ${withdrawalAmount ? (parseFloat(withdrawalAmount) - 2).toFixed(2) : '0.00'} USDT
                </div>
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#1F2937' }}>
                  Network
                </label>
                <select
                  value={selectedNetwork}
                  onChange={(e) => setSelectedNetwork(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #D1D5DB',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                >
                  <option value="TRON">TRON (TRC20)</option>
                  <option value="BSC">Binance Smart Chain (BEP20)</option>
                  <option value="ETH">Ethereum (ERC20)</option>
                  <option value="POLYGON">Polygon (MATIC)</option>
                </select>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#1F2937' }}>
                  USDT Wallet Address
                </label>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="Enter your USDT wallet address"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #D1D5DB',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontFamily: 'monospace'
                  }}
                />
                <div style={{ fontSize: '12px', color: '#EF4444', marginTop: '4px' }}>
                  ⚠️ Double-check your address! Incorrect addresses result in permanent loss of funds.
                </div>
              </div>

              <div style={{ background: '#FEF3C7', padding: '12px', borderRadius: '8px', marginBottom: '20px' }}>
                <div style={{ fontSize: '12px', color: '#92400E' }}>
                  <strong>Important:</strong>
                  <ul style={{ margin: '8px 0', paddingLeft: '16px' }}>
                    <li>Minimum withdrawal: $0.10 USDT</li>
                    <li>Maximum daily withdrawal: $1,000.00 USDT</li>
                    <li>Processing fee: $2.00 USDT</li>
                    <li>Admin review: 24-48 hours</li>
                    <li>Payment processing: 1-3 business days</li>
                  </ul>
                </div>
              </div>

              <div style={{ display: 'flex', gap: '12px' }}>
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowWithdrawModal(false)}
                  style={{ flex: 1 }}
                  disabled={withdrawalLoading}
                >
                  Cancel
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleWithdrawalSubmit}
                  style={{ flex: 1 }}
                  disabled={withdrawalLoading || !withdrawalAmount || !walletAddress}
                >
                  {withdrawalLoading ? 'Processing...' : 'Submit Request'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommissionScreen;