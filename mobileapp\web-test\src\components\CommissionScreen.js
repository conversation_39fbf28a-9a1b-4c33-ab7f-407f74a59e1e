import React, { useState, useEffect } from 'react';
import { dbService } from '../services/supabaseClient';

const CommissionScreen = ({ user }) => {
  const [commissionData, setCommissionData] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCommissionData();
  }, [user]);

  const loadCommissionData = async () => {
    try {
      setLoading(true);
      
      const userData = await dbService.getUserByEmail(user.email);
      
      if (userData) {
        const [commissionBalance, commissionTransactions] = await Promise.all([
          dbService.getCommissionBalance(userData.id),
          dbService.getCommissionTransactions(userData.id),
        ]);

        setCommissionData(commissionBalance);
        setTransactions(commissionTransactions);
      }
    } catch (error) {
      console.error('Error loading commission data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return `${currency} ${parseFloat(amount || 0).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    return parseFloat(number || 0).toLocaleString('en-US');
  };

  const generateReferralLink = () => {
    const baseUrl = 'https://aureus-alliance.com/register';
    const referralCode = user.email.split('@')[0];
    return `${baseUrl}?ref=${referralCode}`;
  };

  const handleShareReferralLink = () => {
    const referralLink = generateReferralLink();
    const message = `Join me on Aureus Alliance Holdings and start purchasing gold shares! Use my referral link: ${referralLink}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Join Aureus Alliance Holdings',
        text: message,
        url: referralLink,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(message).then(() => {
        alert('Referral message copied to clipboard!');
      });
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="screen-container">
      <h2 style={{ margin: '0 0 24px 0', color: '#1F2937' }}>
        Commission Dashboard
      </h2>

      {/* Commission Balances */}
      <div className="commission-balance">
        <div className="balance-card usdt-card">
          <div className="balance-type">💰 USDT Balance</div>
          <div className="balance-amount">
            {formatCurrency(commissionData?.usdt_balance || 0, 'USDT')}
          </div>
          <button className="btn btn-secondary" style={{ marginTop: '12px', fontSize: '12px' }}>
            Withdraw
          </button>
        </div>
        
        <div className="balance-card share-card">
          <div className="balance-type">⭐ Share Balance</div>
          <div className="balance-amount">
            {formatNumber(commissionData?.share_balance || 0)}
          </div>
          <div style={{ fontSize: '10px', color: '#6B7280', marginTop: '8px' }}>
            Added to portfolio
          </div>
        </div>
      </div>

      {/* Total Earnings */}
      <div className="card">
        <div className="card-header">Total Earnings</div>
        
        <div className="stats-grid">
          <div className="stat-item">
            <div className="stat-value" style={{ color: '#26A17B' }}>
              {formatCurrency(commissionData?.total_usdt_earned || 0, 'USDT')}
            </div>
            <div className="stat-label">Total USDT Earned</div>
          </div>
          <div className="stat-item">
            <div className="stat-value" style={{ color: '#FFD700' }}>
              {formatNumber(commissionData?.total_shares_earned || 0)}
            </div>
            <div className="stat-label">Total Shares Earned</div>
          </div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="card">
        <div className="card-header">🔗 Your Referral Link</div>
        
        <p style={{ margin: '0 0 16px 0', color: '#6B7280', fontSize: '14px' }}>
          Share your referral link and earn 15% USDT + 15% shares commission on every share purchase!
        </p>
        
        <div style={{ 
          background: '#F9FAFB',
          border: '1px solid #E5E7EB',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '16px',
          fontSize: '12px',
          fontFamily: 'monospace',
          wordBreak: 'break-all',
          color: '#1E3A8A'
        }}>
          {generateReferralLink()}
        </div>
        
        <button 
          className="btn btn-primary"
          onClick={handleShareReferralLink}
        >
          📤 Share Referral Link
        </button>
      </div>

      {/* Commission History */}
      <div className="card">
        <div className="card-header">Commission History</div>
        
        {transactions && transactions.length > 0 ? (
          <div>
            {transactions.map((transaction, index) => (
              <div key={index} style={{ 
                padding: '16px 0',
                borderBottom: index < transactions.length - 1 ? '1px solid #F3F4F6' : 'none'
              }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'flex-start',
                  marginBottom: '8px'
                }}>
                  <div>
                    <div style={{ fontWeight: '500', color: '#1F2937' }}>
                      Referral Commission
                    </div>
                    <div style={{ fontSize: '12px', color: '#6B7280' }}>
                      {new Date(transaction.payment_date || transaction.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    {transaction.usdt_commission > 0 && (
                      <div style={{ 
                        background: '#ECFDF5',
                        color: '#059669',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '500',
                        marginBottom: '4px'
                      }}>
                        +{formatCurrency(transaction.usdt_commission, 'USDT')}
                      </div>
                    )}
                    {transaction.share_commission > 0 && (
                      <div style={{ 
                        background: '#FEF3C7',
                        color: '#D97706',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}>
                        +{formatNumber(transaction.share_commission)} shares
                      </div>
                    )}
                  </div>
                </div>
                
                {transaction.users && (
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    fontSize: '12px',
                    color: '#6B7280'
                  }}>
                    <span style={{ marginRight: '4px' }}>👤</span>
                    From: {transaction.users.full_name || transaction.users.username}
                  </div>
                )}
                
                <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                  Purchase: {formatCurrency(transaction.share_purchase_amount)}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 16px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>💰</div>
            <h3 style={{ margin: '0 0 8px 0', color: '#1F2937' }}>
              No Commission History
            </h3>
            <p style={{ margin: '0 0 24px 0', color: '#6B7280' }}>
              Start referring friends to earn commission on their share purchases!
            </p>
            <button 
              className="btn btn-primary"
              onClick={handleShareReferralLink}
            >
              Share Referral Link
            </button>
          </div>
        )}
      </div>

      {/* Demo Notice */}
      <div className="info-message">
        <strong>Web Testing Mode:</strong><br/>
        Commission data shown is from your live database. Withdrawal functionality would be implemented in the full mobile app.
      </div>
    </div>
  );
};

export default CommissionScreen;