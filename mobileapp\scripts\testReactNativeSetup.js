#!/usr/bin/env node

/**
 * REACT NATIVE SETUP TEST
 * 
 * This script verifies that React Native is properly configured
 * and all required dependencies are available for Android development.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📱 REACT NATIVE SETUP TEST');
console.log('==========================');
console.log('');

/**
 * Test Node.js version
 */
function testNodeVersion() {
  console.log('🟢 Testing Node.js version...');
  
  try {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    console.log(`   Node.js version: ${nodeVersion}`);
    
    if (majorVersion >= 16) {
      console.log('   ✅ Node.js version is compatible');
      return true;
    } else {
      console.log('   ❌ Node.js version should be 16 or higher');
      return false;
    }
  } catch (error) {
    console.log('   ❌ Failed to check Node.js version');
    return false;
  }
}

/**
 * Test React Native CLI
 */
function testReactNativeCLI() {
  console.log('🟢 Testing React Native CLI...');
  
  try {
    const output = execSync('npx react-native --version', { encoding: 'utf8' });
    console.log(`   React Native CLI: ${output.trim()}`);
    console.log('   ✅ React Native CLI is available');
    return true;
  } catch (error) {
    console.log('   ❌ React Native CLI not found');
    console.log('   💡 Install with: npm install -g @react-native-community/cli');
    return false;
  }
}

/**
 * Test package.json dependencies
 */
function testDependencies() {
  console.log('🟢 Testing package.json dependencies...');
  
  try {
    const packagePath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const requiredDeps = [
      'react',
      'react-native',
      '@supabase/supabase-js',
      'react-native-paper',
      '@react-navigation/native',
      'react-native-vector-icons',
      '@react-native-async-storage/async-storage'
    ];
    
    let missingDeps = [];
    
    for (const dep of requiredDeps) {
      if (!packageJson.dependencies[dep]) {
        missingDeps.push(dep);
      }
    }
    
    if (missingDeps.length === 0) {
      console.log(`   ✅ All ${requiredDeps.length} required dependencies found`);
      return true;
    } else {
      console.log(`   ❌ Missing dependencies: ${missingDeps.join(', ')}`);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Failed to read package.json');
    return false;
  }
}

/**
 * Test Android setup
 */
function testAndroidSetup() {
  console.log('🟢 Testing Android setup...');
  
  try {
    // Check if android folder exists
    const androidPath = path.join(__dirname, '..', 'android');
    if (!fs.existsSync(androidPath)) {
      console.log('   ❌ Android folder not found');
      return false;
    }
    
    // Check if gradlew exists
    const gradlewPath = path.join(androidPath, 'gradlew');
    const gradlewBatPath = path.join(androidPath, 'gradlew.bat');
    
    if (!fs.existsSync(gradlewPath) && !fs.existsSync(gradlewBatPath)) {
      console.log('   ❌ Gradle wrapper not found');
      return false;
    }
    
    console.log('   ✅ Android project structure looks good');
    return true;
  } catch (error) {
    console.log('   ❌ Android setup check failed');
    return false;
  }
}

/**
 * Test environment file
 */
function testEnvironmentFile() {
  console.log('🟢 Testing environment configuration...');
  
  try {
    const envPath = path.join(__dirname, '..', '.env');
    
    if (!fs.existsSync(envPath)) {
      console.log('   ❌ .env file not found');
      return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const requiredVars = [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY'
    ];
    
    let missingVars = [];
    
    for (const varName of requiredVars) {
      if (!envContent.includes(varName)) {
        missingVars.push(varName);
      }
    }
    
    if (missingVars.length === 0) {
      console.log('   ✅ Environment variables configured');
      return true;
    } else {
      console.log(`   ❌ Missing environment variables: ${missingVars.join(', ')}`);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Failed to check environment file');
    return false;
  }
}

/**
 * Test Metro bundler
 */
function testMetroBundler() {
  console.log('🟢 Testing Metro bundler configuration...');
  
  try {
    const metroConfigPath = path.join(__dirname, '..', 'metro.config.js');
    
    if (fs.existsSync(metroConfigPath)) {
      console.log('   ✅ Metro configuration found');
      return true;
    } else {
      console.log('   ⚠️  Metro config not found (using default)');
      return true; // This is okay, React Native will use defaults
    }
  } catch (error) {
    console.log('   ❌ Metro bundler check failed');
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting React Native setup tests...\n');
  
  const tests = [
    { name: 'Node.js Version', fn: testNodeVersion },
    { name: 'React Native CLI', fn: testReactNativeCLI },
    { name: 'Dependencies', fn: testDependencies },
    { name: 'Android Setup', fn: testAndroidSetup },
    { name: 'Environment File', fn: testEnvironmentFile },
    { name: 'Metro Bundler', fn: testMetroBundler },
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = test.fn();
      if (result) {
        passedTests++;
      }
      console.log(''); // Add spacing between tests
    } catch (error) {
      console.error(`❌ ${test.name} failed with exception:`, error.message);
      console.log('');
    }
  }
  
  // Final results
  console.log('📊 SETUP TEST RESULTS');
  console.log('=====================');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('');
    console.log('🎉 REACT NATIVE SETUP IS READY!');
    console.log('');
    console.log('Next steps to test the mobile app:');
    console.log('1. Start Metro bundler: npx react-native start');
    console.log('2. Run on Android: npx react-native run-android');
    console.log('3. Test all app features manually');
  } else {
    console.log('');
    console.log('⚠️  SETUP ISSUES FOUND! Please fix before testing.');
    console.log('');
    console.log('Common solutions:');
    console.log('- Install missing dependencies: npm install');
    console.log('- Update Node.js to version 16+');
    console.log('- Install Android Studio and SDK');
    console.log('- Configure environment variables');
  }
  
  return passedTests === totalTests;
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Setup test failed:', error);
  process.exit(1);
});