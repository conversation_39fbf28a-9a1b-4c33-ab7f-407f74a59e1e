/**
 * Aureus Alliance Holdings Mobile App
 * Portfolio Screen
 * 
 * Displays user's share holdings, investment history, and portfolio performance
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  Surface,
  Searchbar,
  Menu,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, spacing} from '../../constants/theme';

const PortfolioScreen = ({navigation}) => {
  const {
    sharePurchases,
    portfolioSummary,
    currentPhase,
    isLoading,
    isRefreshing,
    refreshPortfolio,
    error,
  } = usePortfolio();

  const [searchQuery, setSearchQuery] = useState('');
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date_desc');

  useEffect(() => {
    if (error) {
      console.error('Portfolio error:', error);
    }
  }, [error]);

  const formatCurrency = (amount, currency = 'USD') => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    if (!number) return '0';
    return parseFloat(number).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  };

  const getFilteredAndSortedPurchases = () => {
    let filtered = sharePurchases || [];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(purchase =>
        purchase.phase_number?.toString().includes(searchQuery) ||
        purchase.status?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        purchase.amount_paid?.toString().includes(searchQuery)
      );
    }

    // Apply status filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(purchase => 
        purchase.status?.toLowerCase() === selectedFilter.toLowerCase()
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date_desc':
          return new Date(b.created_at) - new Date(a.created_at);
        case 'date_asc':
          return new Date(a.created_at) - new Date(b.created_at);
        case 'amount_desc':
          return (b.amount_paid || 0) - (a.amount_paid || 0);
        case 'amount_asc':
          return (a.amount_paid || 0) - (b.amount_paid || 0);
        case 'shares_desc':
          return (b.shares_purchased || 0) - (a.shares_purchased || 0);
        case 'shares_asc':
          return (a.shares_purchased || 0) - (b.shares_purchased || 0);
        default:
          return 0;
      }
    });

    return filtered;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'approved':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'cancelled':
      case 'rejected':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'approved':
        return 'check-circle';
      case 'pending':
        return 'schedule';
      case 'cancelled':
      case 'rejected':
        return 'cancel';
      default:
        return 'help';
    }
  };

  const calculatePortfolioValue = () => {
    const currentSharePrice = currentPhase?.share_price || 5.0;
    const totalShares = portfolioSummary?.totalShares || 0;
    const totalInvested = portfolioSummary?.totalInvested || 0;
    const currentValue = totalShares * currentSharePrice;
    const profitLoss = currentValue - totalInvested;
    const profitLossPercentage = totalInvested > 0 ? (profitLoss / totalInvested) * 100 : 0;

    return {
      currentValue,
      profitLoss,
      profitLossPercentage,
      isProfit: profitLoss >= 0,
    };
  };

  const onRefresh = async () => {
    await refreshPortfolio();
  };

  const handlePurchasePress = (purchase) => {
    // Navigate to purchase details or show modal
    console.log('Purchase pressed:', purchase);
  };

  const renderPurchaseItem = ({item}) => (
    <TouchableOpacity onPress={() => handlePurchasePress(item)}>
      <Card style={styles.purchaseCard}>
        <Card.Content>
          <View style={styles.purchaseHeader}>
            <View style={styles.purchaseInfo}>
              <Text style={styles.purchaseTitle}>
                Phase {item.phase_number || 'N/A'}
              </Text>
              <Text style={styles.purchaseDate}>
                {new Date(item.created_at).toLocaleDateString()}
              </Text>
            </View>
            <Chip
              icon={() => (
                <Icon
                  name={getStatusIcon(item.status)}
                  size={16}
                  color={getStatusColor(item.status)}
                />
              )}
              style={[
                styles.statusChip,
                {backgroundColor: getStatusColor(item.status) + '20'},
              ]}
              textStyle={[
                styles.statusChipText,
                {color: getStatusColor(item.status)},
              ]}>
              {item.status || 'Unknown'}
            </Chip>
          </View>

          <View style={styles.purchaseDetails}>
            <View style={styles.purchaseMetric}>
              <Icon name="trending-up" size={20} color={colors.primary} />
              <View style={styles.metricContent}>
                <Text style={styles.metricValue}>
                  {formatNumber(item.shares_purchased)} shares
                </Text>
                <Text style={styles.metricLabel}>Shares Purchased</Text>
              </View>
            </View>

            <View style={styles.purchaseMetric}>
              <Icon name="attach-money" size={20} color={colors.success} />
              <View style={styles.metricContent}>
                <Text style={styles.metricValue}>
                  {formatCurrency(item.amount_paid)}
                </Text>
                <Text style={styles.metricLabel}>Amount Paid</Text>
              </View>
            </View>
          </View>

          {item.share_price && (
            <View style={styles.priceInfo}>
              <Text style={styles.priceText}>
                Share Price: {formatCurrency(item.share_price)}
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  const filteredPurchases = getFilteredAndSortedPurchases();
  const portfolioValue = calculatePortfolioValue();

  return (
    <View style={styles.container}>
      {/* Portfolio Summary Header */}
      <Surface style={styles.summaryHeader}>
        <View style={styles.summaryContent}>
          <View style={styles.summaryMain}>
            <Text style={styles.summaryLabel}>Portfolio Value</Text>
            <Text style={styles.summaryValue}>
              {formatCurrency(portfolioValue.currentValue)}
            </Text>
            <View style={styles.profitLossContainer}>
              <Icon
                name={portfolioValue.isProfit ? 'trending-up' : 'trending-down'}
                size={16}
                color={portfolioValue.isProfit ? colors.success : colors.error}
              />
              <Text
                style={[
                  styles.profitLossText,
                  {color: portfolioValue.isProfit ? colors.success : colors.error},
                ]}>
                {portfolioValue.isProfit ? '+' : ''}
                {formatCurrency(portfolioValue.profitLoss)} (
                {portfolioValue.profitLossPercentage.toFixed(2)}%)
              </Text>
            </View>
          </View>

          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatNumber(portfolioSummary?.totalShares || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Shares</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatCurrency(portfolioSummary?.totalInvested || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Purchase Value</Text>
            </View>
          </View>
        </View>
      </Surface>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search purchases..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={
            <TouchableOpacity
              style={styles.filterButton}
              onPress={() => setFilterMenuVisible(true)}>
              <Icon name="filter-list" size={24} color={colors.primary} />
            </TouchableOpacity>
          }>
          <Menu.Item
            onPress={() => {
              setSelectedFilter('all');
              setFilterMenuVisible(false);
            }}
            title="All Purchases"
            leadingIcon="all-inclusive"
          />
          <Menu.Item
            onPress={() => {
              setSelectedFilter('active');
              setFilterMenuVisible(false);
            }}
            title="Active"
            leadingIcon="check-circle"
          />
          <Menu.Item
            onPress={() => {
              setSelectedFilter('pending');
              setFilterMenuVisible(false);
            }}
            title="Pending"
            leadingIcon="schedule"
          />
          <Divider />
          <Menu.Item
            onPress={() => {
              setSortBy('date_desc');
              setFilterMenuVisible(false);
            }}
            title="Newest First"
            leadingIcon="sort"
          />
          <Menu.Item
            onPress={() => {
              setSortBy('amount_desc');
              setFilterMenuVisible(false);
            }}
            title="Highest Amount"
            leadingIcon="sort"
          />
        </Menu>
      </View>

      {/* Purchase History */}
      <FlatList
        data={filteredPurchases}
        renderItem={renderPurchaseItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <Icon name="pie-chart" size={60} color={colors.textLight} />
              <Title style={styles.emptyTitle}>No Share Purchases Yet</Title>
              <Paragraph style={styles.emptyDescription}>
                Start building your portfolio by purchasing your first gold shares.
              </Paragraph>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Purchase')}
                style={styles.emptyButton}
                contentStyle={styles.buttonContent}>
                Buy Shares Now
              </Button>
            </Card.Content>
          </Card>
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      {filteredPurchases.length > 0 && (
        <TouchableOpacity
          style={styles.fab}
          onPress={() => navigation.navigate('Purchase')}>
          <Icon name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  summaryHeader: {
    elevation: 4,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  summaryContent: {
    alignItems: 'center',
  },
  summaryMain: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  summaryValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  profitLossContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profitLossText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchBar: {
    flex: 1,
    marginRight: spacing.md,
  },
  filterButton: {
    padding: spacing.sm,
    backgroundColor: colors.surface,
    borderRadius: 8,
    elevation: 2,
  },
  listContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  purchaseCard: {
    marginBottom: spacing.md,
    elevation: 2,
    borderRadius: 12,
  },
  purchaseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  purchaseInfo: {
    flex: 1,
  },
  purchaseTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  purchaseDate: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  statusChip: {
    marginLeft: spacing.md,
  },
  statusChipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  purchaseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  purchaseMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  metricContent: {
    marginLeft: spacing.sm,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
  },
  metricLabel: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  priceInfo: {
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  priceText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  emptyCard: {
    marginTop: spacing.xl,
    elevation: 4,
    borderRadius: 12,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginVertical: spacing.md,
    paddingHorizontal: spacing.md,
  },
  emptyButton: {
    borderRadius: 8,
    marginTop: spacing.md,
  },
  buttonContent: {
    paddingVertical: spacing.xs,
  },
  fab: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
  },
});

export default PortfolioScreen;