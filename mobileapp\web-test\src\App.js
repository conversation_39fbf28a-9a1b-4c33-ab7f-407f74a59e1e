import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';

// Import components
import LoginScreen from './components/LoginScreen';
import DashboardScreen from './components/DashboardScreen';
import PortfolioScreen from './components/PortfolioScreen';
import PurchaseScreen from './components/PurchaseScreen';
import CommissionScreen from './components/CommissionScreen';
import ProfileScreen from './components/ProfileScreen';
import BottomNavigation from './components/BottomNavigation';

// Import services
import { supabase } from './services/supabaseClient';

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentScreen, setCurrentScreen] = useState('dashboard');

  useEffect(() => {
    // Check for existing session
    checkUser();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session) {
          setUser(session.user);
        } else {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
    } catch (error) {
      console.error('Error checking user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setCurrentScreen('dashboard');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading) {
    return (
      <div className="mobile-container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="mobile-container">
        <LoginScreen onLogin={setUser} />
      </div>
    );
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case 'dashboard':
        return <DashboardScreen user={user} />;
      case 'portfolio':
        return <PortfolioScreen user={user} />;
      case 'purchase':
        return <PurchaseScreen user={user} />;
      case 'commission':
        return <CommissionScreen user={user} />;
      case 'profile':
        return <ProfileScreen user={user} onSignOut={handleSignOut} />;
      default:
        return <DashboardScreen user={user} />;
    }
  };

  return (
    <div className="mobile-container">
      <div className="mobile-header">
        Aureus Alliance Holdings
      </div>
      
      <div className="mobile-content">
        {renderScreen()}
      </div>

      <BottomNavigation 
        currentScreen={currentScreen}
        onScreenChange={setCurrentScreen}
      />
    </div>
  );
}

export default App;