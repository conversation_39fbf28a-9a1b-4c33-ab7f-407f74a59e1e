# 🧪 AUREUS ALLIANCE MOBILE APP - TESTING GUIDE

## 📋 **TESTING OVERVIEW**

This guide provides comprehensive testing procedures for the Aureus Alliance Holdings mobile app before production deployment.

---

## 🔧 **TESTING ENVIRONMENT SETUP**

### **Prerequisites**
- ✅ Node.js 16+ (Currently: v20.18.0)
- ✅ React Native development environment
- ✅ Android Studio (for Android testing)
- ✅ Physical Android device or emulator

### **Quick Setup Check**
```bash
# Navigate to mobile app directory
cd mobileapp

# Install dependencies
npm install

# Run setup test
node scripts/testReactNativeSetup.js
```

---

## 🧩 **COMPONENT TESTING**

### **1. Database Integration Test**
```bash
# Test Supabase connection and data access
node scripts/testMobileAppIntegration.js
```

**Expected Results:**
- ✅ Database connection successful
- ✅ Authentication flow working
- ✅ User data access (5 users found)
- ✅ Investment phases (1 active phase)
- ✅ Company wallets (4 active wallets)
- ✅ Share purchases access (RLS working)
- ✅ Real-time subscriptions working

### **2. Component Compilation Test**
Create and run this test to verify all components compile:

```javascript
// Create: mobileapp/scripts/testComponents.js
const components = [
  '../src/screens/auth/LoginScreen.js',
  '../src/screens/auth/RegisterScreen.js', 
  '../src/screens/main/DashboardScreen.js',
  '../src/screens/main/PortfolioScreen.js',
  '../src/screens/main/PurchaseScreen.js',
  '../src/screens/main/CommissionScreen.js',
  '../src/screens/main/ProfileScreen.js',
  '../src/services/supabaseClient.js',
  '../src/context/AuthContext.js',
  '../src/context/PortfolioContext.js'
];

// Test each component imports without errors
components.forEach(component => {
  try {
    require(component);
    console.log(`✅ ${component} - OK`);
  } catch (error) {
    console.log(`❌ ${component} - Error: ${error.message}`);
  }
});
```

---

## 📱 **DEVICE TESTING**

### **Option 1: Android Emulator**
```bash
# Start Android emulator (if available)
# Then run the app
npx react-native run-android
```

### **Option 2: Physical Android Device**
```bash
# Enable USB debugging on your Android device
# Connect via USB
# Run the app
npx react-native run-android
```

### **Option 3: Expo Go (Alternative)**
If React Native setup is complex, you can use Expo:
```bash
# Install Expo CLI
npm install -g @expo/cli

# Initialize Expo project (if needed)
npx create-expo-app --template blank-typescript

# Copy our components to Expo project
# Run with Expo
npx expo start
```

---

## ✅ **MANUAL TESTING CHECKLIST**

### **🔐 Authentication Flow**
- [ ] **Splash Screen**: Loads correctly with Aureus branding
- [ ] **Login Screen**: 
  - [ ] Email/password input works
  - [ ] Validation messages appear for invalid input
  - [ ] "Forgot Password" link works
  - [ ] "Sign Up" link navigates correctly
- [ ] **Registration Screen**:
  - [ ] All form fields accept input
  - [ ] Validation works (email format, password strength)
  - [ ] Terms acceptance checkboxes work
  - [ ] Registration submits successfully
- [ ] **Password Reset**:
  - [ ] Email input validation
  - [ ] Reset email sending (test with real email)

### **📊 Dashboard & Navigation**
- [ ] **Dashboard Screen**:
  - [ ] Portfolio summary displays correctly
  - [ ] Quick actions buttons work
  - [ ] Recent activity shows (if any data)
  - [ ] Pull-to-refresh works
- [ ] **Bottom Navigation**:
  - [ ] All 5 tabs are visible and clickable
  - [ ] Icons display correctly
  - [ ] Active tab highlighting works

### **💰 Portfolio Management**
- [ ] **Portfolio Screen**:
  - [ ] Share holdings display correctly
  - [ ] Investment history loads
  - [ ] Search and filter functionality
  - [ ] Empty state shows when no investments
- [ ] **Purchase Screen**:
  - [ ] Investment phases load and display
  - [ ] Share amount calculation works
  - [ ] Payment wallet selection
  - [ ] Purchase flow completes

### **💸 Commission System**
- [ ] **Commission Screen**:
  - [ ] USDT and share balances display
  - [ ] Commission history loads
  - [ ] Referral link generation works
  - [ ] Share referral link functionality

### **👤 Profile & Settings**
- [ ] **Profile Screen**:
  - [ ] User information displays correctly
  - [ ] Settings toggles work
  - [ ] Sign out functionality
  - [ ] App information shows

### **🔄 Real-time Features**
- [ ] **Data Synchronization**:
  - [ ] Portfolio updates reflect immediately
  - [ ] Commission changes appear in real-time
  - [ ] Network connectivity handling

### **📱 Mobile-Specific Features**
- [ ] **Offline Support**:
  - [ ] App works without internet (cached data)
  - [ ] Graceful handling of network errors
- [ ] **Performance**:
  - [ ] Smooth scrolling and navigation
  - [ ] Fast loading times
  - [ ] No memory leaks or crashes

---

## 🚨 **CRITICAL SECURITY TESTS**

### **Data Protection**
- [ ] **User Isolation**: Users can only see their own data
- [ ] **Authentication**: Unauthenticated users cannot access protected screens
- [ ] **API Security**: All database calls use proper authentication
- [ ] **Local Storage**: Sensitive data is encrypted in AsyncStorage

### **Financial Data Integrity**
- [ ] **Portfolio Accuracy**: Share counts and values match database
- [ ] **Commission Accuracy**: USDT and share balances are correct
- [ ] **Transaction History**: All transactions display accurately
- [ ] **Real-time Sync**: Changes reflect immediately across platforms

---

## 🐛 **COMMON ISSUES & SOLUTIONS**

### **Setup Issues**
```bash
# React Native CLI not found
npm install -g @react-native-community/cli

# Android build issues
cd android
./gradlew clean
cd ..
npx react-native run-android

# Metro bundler issues
npx react-native start --reset-cache
```

### **Runtime Issues**
```bash
# Clear React Native cache
npx react-native start --reset-cache

# Reinstall dependencies
rm -rf node_modules
npm install

# Clear Android build
cd android && ./gradlew clean && cd ..
```

### **Database Connection Issues**
- Check `.env` file has correct Supabase credentials
- Verify network connectivity
- Test with `node scripts/testMobileAppIntegration.js`

---

## 📊 **TESTING RESULTS TEMPLATE**

### **Test Environment**
- **Device**: [Android Version, Device Model]
- **App Version**: 1.0.0
- **Test Date**: [Date]
- **Tester**: [Name]

### **Test Results**
| Feature | Status | Notes |
|---------|--------|-------|
| Authentication | ✅/❌ | |
| Dashboard | ✅/❌ | |
| Portfolio | ✅/❌ | |
| Purchase Flow | ✅/❌ | |
| Commission System | ✅/❌ | |
| Profile Management | ✅/❌ | |
| Real-time Updates | ✅/❌ | |
| Offline Support | ✅/❌ | |

### **Critical Issues Found**
- [ ] None
- [ ] [List any critical issues that block production]

### **Minor Issues Found**
- [ ] [List any minor UI/UX issues]

### **Performance Notes**
- App startup time: [X seconds]
- Navigation responsiveness: [Smooth/Laggy]
- Data loading speed: [Fast/Slow]

---

## 🎯 **PRODUCTION READINESS CRITERIA**

### **Must Pass (Blocking Issues)**
- [ ] All authentication flows work correctly
- [ ] Portfolio data displays accurately
- [ ] Commission tracking is precise
- [ ] No crashes or critical errors
- [ ] Database security (RLS) working properly

### **Should Pass (Important Issues)**
- [ ] Good performance on low-end devices
- [ ] Offline functionality works
- [ ] Real-time updates function correctly
- [ ] Professional UI/UX experience

### **Nice to Have (Minor Issues)**
- [ ] Perfect visual polish
- [ ] Advanced features working
- [ ] Optimal performance

---

## 🚀 **NEXT STEPS AFTER TESTING**

### **If All Tests Pass**
1. Generate production APK: `cd android && ./gradlew assembleRelease`
2. Test production build on multiple devices
3. Submit to Google Play Store
4. Announce to users via Telegram bot

### **If Issues Found**
1. Document all issues with screenshots
2. Prioritize by severity (Critical/Important/Minor)
3. Fix critical issues first
4. Re-test after fixes
5. Repeat until all critical issues resolved

---

## 📞 **SUPPORT DURING TESTING**

If you encounter issues during testing:

1. **Check the logs**: Use `npx react-native log-android` for detailed error logs
2. **Test database separately**: Run `node scripts/testMobileAppIntegration.js`
3. **Verify environment**: Check `.env` file configuration
4. **Clear caches**: Reset Metro bundler and clean Android build

**The mobile app is designed to be production-ready, but thorough testing ensures a smooth launch for your users with invested money.**