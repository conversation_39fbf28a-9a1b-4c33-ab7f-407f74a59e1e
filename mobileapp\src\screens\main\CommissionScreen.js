/**
 * Aureus Alliance Holdings Mobile App
 * Commission Screen
 * 
 * Displays user's commission earnings, referral activity, and withdrawal options
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  FlatList,
  Share,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  Surface,
  Divider,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {usePortfolio} from '../../context/PortfolioContext';
import {useAuth} from '../../context/AuthContext';
import {colors, spacing} from '../../constants/theme';

const CommissionScreen = ({navigation}) => {
  const {user} = useAuth();
  const {
    commissionBalance,
    commissionTransactions,
    isLoading,
    isRefreshing,
    refreshPortfolio,
  } = usePortfolio();

  const [activeTab, setActiveTab] = useState('overview');
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const formatCurrency = (amount, currency = 'USD') => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    if (!number) return '0';
    return parseFloat(number).toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  };

  const generateReferralLink = () => {
    const baseUrl = 'https://aureus-alliance.com/register';
    const referralCode = user?.username || user?.id;
    return `${baseUrl}?ref=${referralCode}`;
  };

  const handleShareReferralLink = async () => {
    try {
      const referralLink = generateReferralLink();
      const message = `Join me on Aureus Alliance Holdings and start purchasing gold shares! Use my referral link: ${referralLink}`;
      
      await Share.share({
        message,
        url: referralLink,
        title: 'Join Aureus Alliance Holdings',
      });
    } catch (error) {
      setSnackbarMessage('Failed to share referral link');
      setSnackbarVisible(true);
    }
  };

  const handleWithdrawal = (type) => {
    // TODO: Implement withdrawal functionality
    setSnackbarMessage(`${type} withdrawal feature coming soon!`);
    setSnackbarVisible(true);
  };

  const onRefresh = async () => {
    await refreshPortfolio();
  };

  const renderCommissionTransaction = ({item}) => (
    <Card style={styles.transactionCard}>
      <Card.Content>
        <View style={styles.transactionHeader}>
          <View style={styles.transactionInfo}>
            <Text style={styles.transactionTitle}>
              Referral Commission
            </Text>
            <Text style={styles.transactionDate}>
              {new Date(item.payment_date || item.created_at).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.transactionAmounts}>
            {item.usdt_commission > 0 && (
              <Chip
                icon="account-balance-wallet"
                style={styles.usdtChip}
                textStyle={styles.usdtChipText}>
                +{formatCurrency(item.usdt_commission, 'USDT')}
              </Chip>
            )}
            {item.share_commission > 0 && (
              <Chip
                icon="stars"
                style={styles.shareChip}
                textStyle={styles.shareChipText}>
                +{formatNumber(item.share_commission)} shares
              </Chip>
            )}
          </View>
        </View>
        
        {item.users && (
          <View style={styles.referredUser}>
            <Icon name="person" size={16} color={colors.textSecondary} />
            <Text style={styles.referredUserText}>
              From: {item.users.full_name || item.users.username}
            </Text>
          </View>
        )}
        
        <Text style={styles.purchaseAmount}>
          Purchase: {formatCurrency(item.share_purchase_amount)}
        </Text>
      </Card.Content>
    </Card>
  );

  const renderOverviewTab = () => (
    <View>
      {/* Commission Balance Cards */}
      <View style={styles.balanceContainer}>
        <Card style={[styles.balanceCard, styles.usdtCard]}>
          <Card.Content>
            <View style={styles.balanceHeader}>
              <Icon name="account-balance-wallet" size={24} color={colors.usdt} />
              <Text style={styles.balanceLabel}>USDT Balance</Text>
            </View>
            <Text style={styles.balanceValue}>
              {formatCurrency(commissionBalance?.usdt_balance || 0, 'USDT')}
            </Text>
            <Button
              mode="outlined"
              onPress={() => handleWithdrawal('USDT')}
              style={styles.withdrawButton}
              contentStyle={styles.withdrawButtonContent}
              labelStyle={styles.withdrawButtonLabel}>
              Withdraw
            </Button>
          </Card.Content>
        </Card>

        <Card style={[styles.balanceCard, styles.shareCard]}>
          <Card.Content>
            <View style={styles.balanceHeader}>
              <Icon name="stars" size={24} color={colors.secondary} />
              <Text style={styles.balanceLabel}>Share Balance</Text>
            </View>
            <Text style={styles.balanceValue}>
              {formatNumber(commissionBalance?.share_balance || 0)}
            </Text>
            <Text style={styles.shareNote}>
              Shares added to portfolio
            </Text>
          </Card.Content>
        </Card>
      </View>

      {/* Total Earnings */}
      <Card style={styles.earningsCard}>
        <Card.Content>
          <Title style={styles.earningsTitle}>Total Earnings</Title>
          <View style={styles.earningsGrid}>
            <View style={styles.earningItem}>
              <Text style={styles.earningValue}>
                {formatCurrency(commissionBalance?.total_usdt_earned || 0, 'USDT')}
              </Text>
              <Text style={styles.earningLabel}>Total USDT Earned</Text>
            </View>
            <View style={styles.earningItem}>
              <Text style={styles.earningValue}>
                {formatNumber(commissionBalance?.total_shares_earned || 0)}
              </Text>
              <Text style={styles.earningLabel}>Total Shares Earned</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Referral Link */}
      <Card style={styles.referralCard}>
        <Card.Content>
          <View style={styles.referralHeader}>
            <Icon name="share" size={24} color={colors.primary} />
            <Title style={styles.referralTitle}>Your Referral Link</Title>
          </View>
          <Paragraph style={styles.referralDescription}>
            Share your referral link and earn 15% USDT + 15% shares commission on every share purchase!
          </Paragraph>
          <Surface style={styles.referralLinkContainer}>
            <Text style={styles.referralLink} numberOfLines={1}>
              {generateReferralLink()}
            </Text>
          </Surface>
          <Button
            mode="contained"
            onPress={handleShareReferralLink}
            style={styles.shareButton}
            contentStyle={styles.shareButtonContent}
            icon="share">
            Share Referral Link
          </Button>
        </Card.Content>
      </Card>
    </View>
  );

  const renderTransactionsTab = () => (
    <View style={styles.transactionsContainer}>
      {commissionTransactions && commissionTransactions.length > 0 ? (
        <FlatList
          data={commissionTransactions}
          renderItem={renderCommissionTransaction}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.transactionsList}
        />
      ) : (
        <Card style={styles.emptyCard}>
          <Card.Content style={styles.emptyContent}>
            <Icon name="monetization-on" size={60} color={colors.textLight} />
            <Title style={styles.emptyTitle}>No Commission History</Title>
            <Paragraph style={styles.emptyDescription}>
              Start referring friends to earn commission on their share purchases!
            </Paragraph>
            <Button
              mode="contained"
              onPress={handleShareReferralLink}
              style={styles.emptyButton}
              contentStyle={styles.buttonContent}>
              Share Referral Link
            </Button>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Tab Navigation */}
      <Surface style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
          onPress={() => setActiveTab('overview')}>
          <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'transactions' && styles.activeTab]}
          onPress={() => setActiveTab('transactions')}>
          <Text style={[styles.tabText, activeTab === 'transactions' && styles.activeTabText]}>
            History
          </Text>
        </TouchableOpacity>
      </Surface>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }>
        {activeTab === 'overview' ? renderOverviewTab() : renderTransactionsTab()}
      </ScrollView>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  tabContainer: {
    flexDirection: 'row',
    elevation: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    backgroundColor: colors.surface,
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  balanceContainer: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  balanceCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
    elevation: 4,
    borderRadius: 12,
  },
  usdtCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.usdt,
  },
  shareCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.secondary,
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  balanceLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  balanceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  shareNote: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  withdrawButton: {
    borderRadius: 6,
    borderColor: colors.primary,
  },
  withdrawButtonContent: {
    paddingVertical: spacing.xs,
  },
  withdrawButtonLabel: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  earningsCard: {
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  earningsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  earningsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  earningItem: {
    alignItems: 'center',
  },
  earningValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  earningLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  referralCard: {
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 12,
  },
  referralHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  referralTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginLeft: spacing.sm,
  },
  referralDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  referralLinkContainer: {
    padding: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.background,
    marginBottom: spacing.md,
  },
  referralLink: {
    fontSize: 14,
    color: colors.primary,
    fontFamily: 'monospace',
  },
  shareButton: {
    borderRadius: 8,
  },
  shareButtonContent: {
    paddingVertical: spacing.xs,
  },
  transactionsContainer: {
    flex: 1,
  },
  transactionsList: {
    paddingBottom: spacing.lg,
  },
  transactionCard: {
    marginBottom: spacing.md,
    elevation: 2,
    borderRadius: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
  },
  transactionDate: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  transactionAmounts: {
    alignItems: 'flex-end',
  },
  usdtChip: {
    backgroundColor: colors.usdt + '20',
    marginBottom: spacing.xs,
  },
  usdtChipText: {
    color: colors.usdt,
    fontWeight: 'bold',
    fontSize: 12,
  },
  shareChip: {
    backgroundColor: colors.secondary + '20',
  },
  shareChipText: {
    color: colors.primary,
    fontWeight: 'bold',
    fontSize: 12,
  },
  referredUser: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  referredUserText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  purchaseAmount: {
    fontSize: 12,
    color: colors.textLight,
  },
  emptyCard: {
    elevation: 4,
    borderRadius: 12,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginVertical: spacing.md,
    paddingHorizontal: spacing.md,
  },
  emptyButton: {
    borderRadius: 8,
    marginTop: spacing.md,
  },
  buttonContent: {
    paddingVertical: spacing.xs,
  },
  snackbar: {
    backgroundColor: colors.primary,
  },
});

export default CommissionScreen;