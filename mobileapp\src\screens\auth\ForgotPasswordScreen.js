/**
 * Aureus Alliance Holdings Mobile App
 * Forgot Password Screen
 * 
 * Allows users to reset their password via email
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  Paragraph,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {colors, spacing} from '../../constants/theme';

const ForgotPasswordScreen = ({navigation}) => {
  const [email, setEmail] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const {resetPassword, isLoading, error, clearError} = useAuth();

  useEffect(() => {
    if (error) {
      setSnackbarMessage(error);
      setSnackbarVisible(true);
      clearError();
    }
  }, [error]);

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    if (!email.trim()) {
      setSnackbarMessage('Please enter your email address');
      setSnackbarVisible(true);
      return;
    }

    if (!isValidEmail(email)) {
      setSnackbarMessage('Please enter a valid email address');
      setSnackbarVisible(true);
      return;
    }

    const result = await resetPassword(email.trim().toLowerCase());
    
    if (result.success) {
      setEmailSent(true);
      setSnackbarMessage('Password reset email sent! Check your inbox.');
      setSnackbarVisible(true);
    } else {
      setSnackbarMessage(result.error || 'Failed to send reset email. Please try again.');
      setSnackbarVisible(true);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const handleResendEmail = () => {
    setEmailSent(false);
    handleResetPassword();
  };

  if (emailSent) {
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled">
          
          {/* Success Header */}
          <View style={styles.header}>
            <Icon name="mark-email-read" size={80} color={colors.success} />
            <Title style={styles.title}>Check Your Email</Title>
            <Paragraph style={styles.subtitle}>
              We've sent a password reset link to:
            </Paragraph>
            <Text style={styles.emailText}>{email}</Text>
          </View>

          {/* Instructions Card */}
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.instructionsContainer}>
                <Text style={styles.instructionsTitle}>Next Steps:</Text>
                
                <View style={styles.step}>
                  <Icon name="looks-one" size={24} color={colors.primary} />
                  <Text style={styles.stepText}>
                    Check your email inbox (and spam folder)
                  </Text>
                </View>
                
                <View style={styles.step}>
                  <Icon name="looks-two" size={24} color={colors.primary} />
                  <Text style={styles.stepText}>
                    Click the password reset link in the email
                  </Text>
                </View>
                
                <View style={styles.step}>
                  <Icon name="looks-3" size={24} color={colors.primary} />
                  <Text style={styles.stepText}>
                    Create a new password and sign in
                  </Text>
                </View>
              </View>

              <Button
                mode="outlined"
                onPress={handleResendEmail}
                loading={isLoading}
                disabled={isLoading}
                style={styles.resendButton}
                contentStyle={styles.buttonContent}>
                Resend Email
              </Button>

              <Button
                mode="contained"
                onPress={handleBackToLogin}
                style={styles.backButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}>
                Back to Sign In
              </Button>
            </Card.Content>
          </Card>
        </ScrollView>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={4000}
          style={styles.snackbar}>
          {snackbarMessage}
        </Snackbar>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        
        {/* Header */}
        <View style={styles.header}>
          <Icon name="lock-reset" size={60} color={colors.warning} />
          <Title style={styles.title}>Reset Password</Title>
          <Paragraph style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Paragraph>
        </View>

        {/* Reset Form */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.form}>
              {/* Email Input */}
              <TextInput
                label="Email Address"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                autoComplete="email"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Reset Button */}
              <Button
                mode="contained"
                onPress={handleResetPassword}
                loading={isLoading}
                disabled={isLoading}
                style={styles.resetButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}>
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>

              {/* Back to Login Link */}
              <View style={styles.backToLoginContainer}>
                <Text style={styles.backToLoginText}>Remember your password? </Text>
                <TouchableOpacity onPress={handleBackToLogin}>
                  <Text style={styles.backToLoginLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Help Card */}
        <Card style={styles.helpCard}>
          <Card.Content>
            <View style={styles.helpContent}>
              <Icon name="help-outline" size={24} color={colors.info} />
              <View style={styles.helpText}>
                <Text style={styles.helpTitle}>
                  Need Help?
                </Text>
                <Text style={styles.helpDescription}>
                  If you don't receive the email within a few minutes, check your spam folder or contact support.
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Snackbar for messages */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: spacing.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  emailText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  card: {
    elevation: 4,
    borderRadius: 12,
    marginBottom: spacing.lg,
  },
  form: {
    paddingVertical: spacing.md,
  },
  input: {
    marginBottom: spacing.lg,
  },
  resetButton: {
    borderRadius: 8,
    marginBottom: spacing.lg,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  backToLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backToLoginText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  backToLoginLink: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    marginBottom: spacing.lg,
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    color: colors.text,
    marginLeft: spacing.md,
    lineHeight: 20,
  },
  resendButton: {
    borderRadius: 8,
    marginBottom: spacing.md,
  },
  backButton: {
    borderRadius: 8,
  },
  helpCard: {
    elevation: 2,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  helpContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  helpText: {
    flex: 1,
    marginLeft: spacing.md,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.info,
    marginBottom: spacing.xs,
  },
  helpDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  snackbar: {
    backgroundColor: colors.error,
  },
});

export default ForgotPasswordScreen;