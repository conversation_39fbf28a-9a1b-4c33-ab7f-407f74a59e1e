import React, { useEffect, useState } from 'react';

const SplashScreen = ({ onComplete }) => {
  const [showContent, setShowContent] = useState(false);
  const [particles, setParticles] = useState([]);

  useEffect(() => {
    // Create initial particles
    const initialParticles = [];
    for (let i = 0; i < 50; i++) {
      initialParticles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        size: Math.random() * 4 + 1,
        speedX: (Math.random() - 0.5) * 2,
        speedY: (Math.random() - 0.5) * 2,
        opacity: Math.random() * 0.8 + 0.2,
        life: Math.random() * 100 + 50
      });
    }
    setParticles(initialParticles);

    // Show content after particles settle
    const contentTimer = setTimeout(() => {
      setShowContent(true);
    }, 1000);

    // Complete splash screen
    const completeTimer = setTimeout(() => {
      onComplete();
    }, 4000);

    // Animate particles
    const animateParticles = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => ({
          ...particle,
          x: particle.x + particle.speedX,
          y: particle.y + particle.speedY,
          life: particle.life - 1,
          opacity: particle.life > 20 ? particle.opacity : particle.opacity * 0.95
        })).filter(particle => particle.life > 0)
      );
    };

    const animationInterval = setInterval(animateParticles, 50);

    return () => {
      clearTimeout(contentTimer);
      clearTimeout(completeTimer);
      clearInterval(animationInterval);
    };
  }, [onComplete]);

  return (
    <div className="splash-screen">
      {/* Particle Canvas */}
      <div className="particles-container">
        {particles.map(particle => (
          <div
            key={particle.id}
            className="particle"
            style={{
              left: `${particle.x}px`,
              top: `${particle.y}px`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              opacity: particle.opacity,
              background: `radial-gradient(circle, #FFD700 0%, #FFA500 50%, transparent 100%)`,
              borderRadius: '50%',
              position: 'absolute',
              pointerEvents: 'none',
              boxShadow: `0 0 ${particle.size * 2}px #FFD700`,
              animation: `twinkle ${Math.random() * 2 + 1}s infinite alternate`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className={`splash-content ${showContent ? 'show' : ''}`}>
        {/* Logo Container */}
        <div className="logo-container">
          <div className="logo-placeholder">
            <span className="logo-text">AU</span>
          </div>
          <div className="logo-glow"></div>
        </div>

        {/* App Info */}
        <div className="app-info">
          <div className="website">www.aureus.africa</div>
          <div className="version">Version 1.0.0</div>
        </div>

        {/* Loading Animation */}
        <div className="loading-container">
          <div className="loading-bar">
            <div className="loading-progress"></div>
          </div>
          <div className="loading-text">Loading your gold investment platform...</div>
        </div>
      </div>

      {/* Background Effects */}
      <div className="background-effects">
        <div className="gold-gradient"></div>
        <div className="shimmer-effect"></div>
      </div>
    </div>
  );
};

export default SplashScreen;