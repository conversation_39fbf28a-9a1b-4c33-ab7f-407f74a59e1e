import React, { useState, useEffect } from 'react';
import { dbService } from '../services/supabaseClient';

const PurchaseScreen = ({ user }) => {
  const [phases, setPhases] = useState([]);
  const [selectedPhase, setSelectedPhase] = useState(null);
  const [shareAmount, setShareAmount] = useState('');
  const [wallets, setWallets] = useState([]);
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPurchaseData();
  }, []);

  const loadPurchaseData = async () => {
    try {
      setLoading(true);
      
      const [allPhases, companyWallets] = await Promise.all([
        dbService.getAllPhases(),
        dbService.getCompanyWallets(),
      ]);

      setPhases(allPhases);
      setWallets(companyWallets);
      
      // Auto-select current phase
      const currentPhase = allPhases.find(p => p.is_active);
      if (currentPhase) {
        setSelectedPhase(currentPhase);
      }
    } catch (error) {
      console.error('Error loading purchase data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    return parseFloat(number || 0).toLocaleString('en-US');
  };

  const calculateTotal = () => {
    if (!selectedPhase || !shareAmount) return 0;
    return parseFloat(shareAmount) * (selectedPhase.share_price || 0);
  };

  const handlePurchase = () => {
    if (!selectedPhase || !shareAmount || !selectedWallet) {
      alert('Please complete all fields before purchasing.');
      return;
    }

    const total = calculateTotal();
    alert(`Purchase simulation:\n\n${shareAmount} shares from Phase ${selectedPhase.phase_number}\nTotal: ${formatCurrency(total)}\nPayment to: ${selectedWallet.network}\n\nIn the real app, this would create a purchase record and show payment instructions.`);
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="screen-container">
      <h2 style={{ margin: '0 0 24px 0', color: '#1F2937' }}>
        Buy Gold Shares
      </h2>

      {/* Phase Selection */}
      <div className="card">
        <div className="card-header">Select Purchase Phase</div>
        
        <div className="phase-selector">
          {phases.map((phase) => (
            <div
              key={phase.id}
              className={`phase-option ${selectedPhase?.id === phase.id ? 'selected' : ''}`}
              onClick={() => setSelectedPhase(phase)}
            >
              <div className="phase-header">
                <div className="phase-number">
                  Phase {phase.phase_number}
                  {phase.is_active && (
                    <span style={{ 
                      marginLeft: '8px',
                      padding: '2px 6px',
                      background: '#FFD700',
                      color: '#1E3A8A',
                      borderRadius: '4px',
                      fontSize: '10px',
                      fontWeight: 'bold'
                    }}>
                      CURRENT
                    </span>
                  )}
                </div>
              </div>
              <div className="phase-price">
                {formatCurrency(phase.share_price)} per share
              </div>
              <div style={{ fontSize: '14px', color: '#6B7280' }}>
                {formatNumber(phase.total_shares - (phase.shares_sold || 0))} shares available
              </div>
              {phase.description && (
                <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                  {phase.description}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Share Amount Input */}
      {selectedPhase && (
        <div className="card">
          <div className="card-header">Enter Share Amount</div>
          
          <div className="form-group">
            <label className="form-label">Number of Shares</label>
            <input
              type="number"
              className="form-input"
              value={shareAmount}
              onChange={(e) => setShareAmount(e.target.value)}
              placeholder="Enter number of shares"
              min="1"
            />
          </div>

          {shareAmount && (
            <div className="calculation-summary">
              <div className="calculation-row">
                <span>Shares:</span>
                <span>{formatNumber(shareAmount)}</span>
              </div>
              <div className="calculation-row">
                <span>Price per share:</span>
                <span>{formatCurrency(selectedPhase.share_price)}</span>
              </div>
              <div className="calculation-row calculation-total">
                <span>Total Cost:</span>
                <span style={{ color: '#1E3A8A', fontWeight: 'bold' }}>
                  {formatCurrency(calculateTotal())}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Payment Wallet Selection */}
      {shareAmount && calculateTotal() > 0 && (
        <div className="card">
          <div className="card-header">Select Payment Wallet</div>
          
          {wallets.length > 0 ? (
            <div>
              {wallets.map((wallet) => (
                <div
                  key={wallet.id}
                  className={`phase-option ${selectedWallet?.id === wallet.id ? 'selected' : ''}`}
                  onClick={() => setSelectedWallet(wallet)}
                  style={{ marginBottom: '12px' }}
                >
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    marginBottom: '8px'
                  }}>
                    <span style={{ fontSize: '20px', marginRight: '8px' }}>💳</span>
                    <span style={{ fontWeight: 'bold', color: '#1F2937' }}>
                      {wallet.network}
                    </span>
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#6B7280',
                    fontFamily: 'monospace',
                    wordBreak: 'break-all'
                  }}>
                    {wallet.wallet_address}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#6B7280' }}>
              No payment wallets available. Please contact support.
            </div>
          )}
        </div>
      )}

      {/* Purchase Button */}
      {shareAmount && selectedWallet && calculateTotal() > 0 && (
        <div className="card">
          <button 
            className="btn btn-primary"
            onClick={handlePurchase}
          >
            Purchase {shareAmount} Shares - {formatCurrency(calculateTotal())}
          </button>
          
          <div style={{ 
            fontSize: '12px', 
            color: '#6B7280', 
            textAlign: 'center',
            marginTop: '12px',
            lineHeight: '1.4'
          }}>
            After confirming, you'll receive payment instructions and need to upload proof of payment.
          </div>
        </div>
      )}

      {/* Demo Notice */}
      <div className="info-message">
        <strong>Web Testing Mode:</strong><br/>
        This is a demonstration of the purchase flow. In the real mobile app, this would create actual purchase records and integrate with payment processing.
      </div>
    </div>
  );
};

export default PurchaseScreen;