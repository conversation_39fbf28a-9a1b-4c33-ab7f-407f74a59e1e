/**
 * Aureus Alliance Holdings Mobile App
 * Profile Screen
 * 
 * User profile management, settings, and account information
 */

import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Avatar,
  List,
  Divider,
  Switch,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {usePortfolio} from '../../context/PortfolioContext';
import {colors, spacing} from '../../constants/theme';

const ProfileScreen = ({navigation}) => {
  const {user, signOut} = useAuth();
  const {portfolioSummary} = usePortfolio();
  
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricsEnabled, setBiometricsEnabled] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const formatCurrency = (amount, currency = 'USD') => {
    if (!amount) return `${currency} 0.00`;
    return `${currency} ${parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    if (!number) return '0';
    return parseFloat(number).toLocaleString('en-US');
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const result = await signOut();
            if (!result.success) {
              setSnackbarMessage('Failed to sign out. Please try again.');
              setSnackbarVisible(true);
            }
          },
        },
      ]
    );
  };

  const handleSettingChange = (setting, value) => {
    switch (setting) {
      case 'notifications':
        setNotificationsEnabled(value);
        setSnackbarMessage(
          value ? 'Notifications enabled' : 'Notifications disabled'
        );
        setSnackbarVisible(true);
        break;
      case 'biometrics':
        setBiometricsEnabled(value);
        setSnackbarMessage(
          value ? 'Biometric authentication enabled' : 'Biometric authentication disabled'
        );
        setSnackbarVisible(true);
        break;
      default:
        break;
    }
  };

  const handleMenuPress = (item) => {
    switch (item) {
      case 'edit_profile':
        setSnackbarMessage('Edit profile feature coming soon!');
        setSnackbarVisible(true);
        break;
      case 'security':
        setSnackbarMessage('Security settings coming soon!');
        setSnackbarVisible(true);
        break;
      case 'kyc':
        setSnackbarMessage('KYC verification coming soon!');
        setSnackbarVisible(true);
        break;
      case 'support':
        setSnackbarMessage('Support feature coming soon!');
        setSnackbarVisible(true);
        break;
      case 'about':
        setSnackbarMessage('About page coming soon!');
        setSnackbarVisible(true);
        break;
      default:
        break;
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Profile Header */}
      <Card style={styles.profileCard}>
        <Card.Content>
          <View style={styles.profileHeader}>
            <Avatar.Text
              size={80}
              label={user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'U'}
              style={styles.avatar}
            />
            <View style={styles.profileInfo}>
              <Title style={styles.userName}>
                {user?.full_name || user?.username || 'User'}
              </Title>
              <Paragraph style={styles.userEmail}>
                {user?.email || 'No email'}
              </Paragraph>
              {user?.username && (
                <Paragraph style={styles.userUsername}>
                  @{user.username}
                </Paragraph>
              )}
            </View>
          </View>

          {/* Quick Stats */}
          <View style={styles.quickStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatNumber(portfolioSummary?.totalShares || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Shares</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatCurrency(portfolioSummary?.totalInvested || 0)}
              </Text>
              <Text style={styles.statLabel}>Total Purchase Value</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Account Management */}
      <Card style={styles.menuCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Account Management</Title>
          
          <List.Item
            title="Edit Profile"
            description="Update your personal information"
            left={(props) => <List.Icon {...props} icon="account-edit" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => handleMenuPress('edit_profile')}
            style={styles.menuItem}
          />
          
          <Divider />
          
          <List.Item
            title="Security Settings"
            description="Password and security options"
            left={(props) => <List.Icon {...props} icon="security" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => handleMenuPress('security')}
            style={styles.menuItem}
          />
          
          <Divider />
          
          <List.Item
            title="KYC Verification"
            description="Complete identity verification"
            left={(props) => <List.Icon {...props} icon="verified-user" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => handleMenuPress('kyc')}
            style={styles.menuItem}
          />
        </Card.Content>
      </Card>

      {/* App Settings */}
      <Card style={styles.menuCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>App Settings</Title>
          
          <List.Item
            title="Push Notifications"
            description="Receive share purchase and commission updates"
            left={(props) => <List.Icon {...props} icon="notifications" />}
            right={() => (
              <Switch
                value={notificationsEnabled}
                onValueChange={(value) => handleSettingChange('notifications', value)}
                color={colors.primary}
              />
            )}
            style={styles.menuItem}
          />
          
          <Divider />
          
          <List.Item
            title="Biometric Authentication"
            description="Use fingerprint or face ID to sign in"
            left={(props) => <List.Icon {...props} icon="fingerprint" />}
            right={() => (
              <Switch
                value={biometricsEnabled}
                onValueChange={(value) => handleSettingChange('biometrics', value)}
                color={colors.primary}
              />
            )}
            style={styles.menuItem}
          />
        </Card.Content>
      </Card>

      {/* Support & Information */}
      <Card style={styles.menuCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Support & Information</Title>
          
          <List.Item
            title="Help & Support"
            description="Get help with your account"
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => handleMenuPress('support')}
            style={styles.menuItem}
          />
          
          <Divider />
          
          <List.Item
            title="About Aureus Alliance"
            description="Learn more about our platform"
            left={(props) => <List.Icon {...props} icon="information" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => handleMenuPress('about')}
            style={styles.menuItem}
          />
        </Card.Content>
      </Card>

      {/* App Information */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <View style={styles.appInfo}>
            <Text style={styles.appName}>Aureus Alliance Holdings</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              Secure gold share purchase platform
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Sign Out Button */}
      <Button
        mode="outlined"
        onPress={handleSignOut}
        style={styles.signOutButton}
        contentStyle={styles.signOutButtonContent}
        labelStyle={styles.signOutButtonLabel}
        icon="logout">
        Sign Out
      </Button>

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  profileCard: {
    marginBottom: spacing.lg,
    elevation: 4,
    borderRadius: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  avatar: {
    backgroundColor: colors.primary,
  },
  profileInfo: {
    flex: 1,
    marginLeft: spacing.lg,
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  userUsername: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: colors.border,
  },
  menuCard: {
    marginBottom: spacing.lg,
    elevation: 2,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  menuItem: {
    paddingVertical: spacing.sm,
  },
  infoCard: {
    marginBottom: spacing.lg,
    elevation: 1,
    borderRadius: 12,
    backgroundColor: colors.surface,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  appName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  appVersion: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  appDescription: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: 'center',
  },
  signOutButton: {
    borderRadius: 8,
    borderColor: colors.error,
    marginBottom: spacing.lg,
  },
  signOutButtonContent: {
    paddingVertical: spacing.sm,
  },
  signOutButtonLabel: {
    color: colors.error,
    fontWeight: 'bold',
  },
  snackbar: {
    backgroundColor: colors.primary,
  },
});

export default ProfileScreen;