import React, { useState, useEffect } from 'react';
import { dbService } from '../services/supabaseClient';

const PortfolioScreen = ({ user }) => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPortfolioData();
  }, [user]);

  const loadPortfolioData = async () => {
    try {
      setLoading(true);
      
      const userData = await dbService.getUserByEmail(user.email);
      
      if (userData) {
        const sharePurchases = await dbService.getUserSharePurchases(userData.id);
        
        const totalShares = sharePurchases.reduce((sum, purchase) => 
          sum + (purchase.shares_purchased || 0), 0);
        const totalInvested = sharePurchases.reduce((sum, purchase) => 
          sum + (purchase.amount_paid || 0), 0);

        setPortfolioData({
          totalShares,
          totalInvested,
          sharePurchases,
          currentValue: totalShares * 5.0, // Assuming $5 per share
        });
      }
    } catch (error) {
      console.error('Error loading portfolio data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatNumber = (number) => {
    return parseFloat(number || 0).toLocaleString('en-US');
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return '#10B981';
      case 'pending':
        return '#F59E0B';
      case 'cancelled':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  const profitLoss = (portfolioData?.currentValue || 0) - (portfolioData?.totalInvested || 0);
  const profitLossPercentage = portfolioData?.totalInvested > 0 
    ? (profitLoss / portfolioData.totalInvested) * 100 
    : 0;

  return (
    <div className="screen-container">
      {/* Portfolio Summary */}
      <div className="portfolio-summary">
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          <div style={{ fontSize: '14px', opacity: '0.8' }}>Portfolio Value</div>
          <div className="portfolio-value">
            {formatCurrency(portfolioData?.currentValue || 0)}
          </div>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            fontSize: '14px'
          }}>
            <span style={{ marginRight: '4px' }}>
              {profitLoss >= 0 ? '📈' : '📉'}
            </span>
            {profitLoss >= 0 ? '+' : ''}{formatCurrency(profitLoss)} ({profitLossPercentage.toFixed(2)}%)
          </div>
        </div>

        <div className="stats-grid">
          <div className="stat-item">
            <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
              {formatNumber(portfolioData?.totalShares || 0)}
            </div>
            <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
              Total Shares
            </div>
          </div>
          <div className="stat-item">
            <div style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
              {formatCurrency(portfolioData?.totalInvested || 0)}
            </div>
            <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
              Total Purchase Value
            </div>
          </div>
        </div>
      </div>

      {/* Purchase History */}
      <div className="card">
        <div className="card-header">Share Purchase History</div>
        
        {portfolioData?.sharePurchases && portfolioData.sharePurchases.length > 0 ? (
          <div>
            {portfolioData.sharePurchases.map((purchase, index) => (
              <div key={index} style={{ 
                padding: '16px 0',
                borderBottom: index < portfolioData.sharePurchases.length - 1 ? '1px solid #F3F4F6' : 'none'
              }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'flex-start',
                  marginBottom: '8px'
                }}>
                  <div>
                    <div style={{ fontWeight: '500', color: '#1F2937' }}>
                      Phase {purchase.phase_number || 'N/A'}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6B7280' }}>
                      {new Date(purchase.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div style={{ 
                    padding: '4px 8px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: getStatusColor(purchase.status)
                  }}>
                    {purchase.status}
                  </div>
                </div>
                
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  fontSize: '14px',
                  color: '#6B7280'
                }}>
                  <div>
                    <span style={{ marginRight: '16px' }}>
                      📈 {formatNumber(purchase.shares_purchased)} shares
                    </span>
                    <span>
                      💰 {formatCurrency(purchase.amount_paid)}
                    </span>
                  </div>
                  {purchase.share_price && (
                    <div>
                      {formatCurrency(purchase.share_price)}/share
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 16px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
            <h3 style={{ margin: '0 0 8px 0', color: '#1F2937' }}>
              No Share Purchases Yet
            </h3>
            <p style={{ margin: '0 0 24px 0', color: '#6B7280' }}>
              Start building your portfolio by purchasing your first gold shares.
            </p>
            <button className="btn btn-primary">
              Buy Shares Now
            </button>
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      {portfolioData?.sharePurchases && portfolioData.sharePurchases.length > 0 && (
        <div className="card">
          <div className="card-header">Performance Metrics</div>
          
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-value" style={{ color: profitLoss >= 0 ? '#10B981' : '#EF4444' }}>
                {profitLoss >= 0 ? '+' : ''}{formatCurrency(profitLoss)}
              </div>
              <div className="stat-label">Unrealized P&L</div>
            </div>
            <div className="stat-item">
              <div className="stat-value">
                {portfolioData.sharePurchases.filter(p => p.status === 'active').length}
              </div>
              <div className="stat-label">Active Purchases</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortfolioScreen;