/**
 * Aureus Alliance Holdings Mobile App
 * Registration Screen
 * 
 * Allows new users to create accounts
 * Integrates with existing Telegram bot user system
 */

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  Paragraph,
  Checkbox,
  Snackbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../context/AuthContext';
import {colors, spacing} from '../../constants/theme';

const RegisterScreen = ({navigation}) => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    phoneNumber: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const {signUp, isLoading, error, clearError} = useAuth();

  useEffect(() => {
    if (error) {
      setSnackbarMessage(error);
      setSnackbarVisible(true);
      clearError();
    }
  }, [error]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    const {fullName, email, password, confirmPassword, username} = formData;

    if (!fullName.trim()) {
      return 'Full name is required';
    }

    if (!email.trim()) {
      return 'Email address is required';
    }

    if (!isValidEmail(email)) {
      return 'Please enter a valid email address';
    }

    if (!username.trim()) {
      return 'Username is required';
    }

    if (username.length < 3) {
      return 'Username must be at least 3 characters';
    }

    if (!password) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }

    if (password !== confirmPassword) {
      return 'Passwords do not match';
    }

    if (!acceptedTerms) {
      return 'Please accept the Terms of Service';
    }

    if (!acceptedPrivacy) {
      return 'Please accept the Privacy Policy';
    }

    return null;
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleRegister = async () => {
    const validationError = validateForm();
    if (validationError) {
      setSnackbarMessage(validationError);
      setSnackbarVisible(true);
      return;
    }

    const userData = {
      full_name: formData.fullName.trim(),
      username: formData.username.trim().toLowerCase(),
      phone_number: formData.phoneNumber.trim(),
    };

    const result = await signUp(
      formData.email.trim().toLowerCase(),
      formData.password,
      userData
    );

    if (result.success) {
      setSnackbarMessage('Registration successful! Please check your email for verification.');
      setSnackbarVisible(true);
      // Navigation will be handled by auth state change
    } else {
      setSnackbarMessage(result.error || 'Registration failed. Please try again.');
      setSnackbarVisible(true);
    }
  };

  const handleSignIn = () => {
    navigation.navigate('Login');
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled">
        
        {/* Header */}
        <View style={styles.header}>
          <Icon name="account-circle" size={60} color={colors.secondary} />
          <Title style={styles.title}>Create Account</Title>
          <Paragraph style={styles.subtitle}>
            Join Aureus Alliance Holdings and start your gold share purchase journey
          </Paragraph>
        </View>

        {/* Registration Form */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.form}>
              {/* Full Name */}
              <TextInput
                label="Full Name *"
                value={formData.fullName}
                onChangeText={(value) => handleInputChange('fullName', value)}
                mode="outlined"
                autoCapitalize="words"
                autoCorrect={false}
                left={<TextInput.Icon icon="account" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Email */}
              <TextInput
                label="Email Address *"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                autoComplete="email"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Username */}
              <TextInput
                label="Username *"
                value={formData.username}
                onChangeText={(value) => handleInputChange('username', value)}
                mode="outlined"
                autoCapitalize="none"
                autoCorrect={false}
                left={<TextInput.Icon icon="at" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
                helperText="Minimum 3 characters, will be used for referrals"
              />

              {/* Phone Number */}
              <TextInput
                label="Phone Number (Optional)"
                value={formData.phoneNumber}
                onChangeText={(value) => handleInputChange('phoneNumber', value)}
                mode="outlined"
                keyboardType="phone-pad"
                autoComplete="tel"
                left={<TextInput.Icon icon="phone" />}
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Password */}
              <TextInput
                label="Password *"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
                helperText="Minimum 8 characters"
              />

              {/* Confirm Password */}
              <TextInput
                label="Confirm Password *"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                mode="outlined"
                secureTextEntry={!showConfirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
                left={<TextInput.Icon icon="lock-check" />}
                right={
                  <TextInput.Icon
                    icon={showConfirmPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  />
                }
                style={styles.input}
                theme={{colors: {primary: colors.primary}}}
              />

              {/* Terms and Conditions */}
              <View style={styles.checkboxContainer}>
                <Checkbox
                  status={acceptedTerms ? 'checked' : 'unchecked'}
                  onPress={() => setAcceptedTerms(!acceptedTerms)}
                  color={colors.primary}
                />
                <View style={styles.checkboxText}>
                  <Text style={styles.checkboxLabel}>
                    I accept the{' '}
                    <Text style={styles.link}>Terms of Service</Text>
                  </Text>
                </View>
              </View>

              <View style={styles.checkboxContainer}>
                <Checkbox
                  status={acceptedPrivacy ? 'checked' : 'unchecked'}
                  onPress={() => setAcceptedPrivacy(!acceptedPrivacy)}
                  color={colors.primary}
                />
                <View style={styles.checkboxText}>
                  <Text style={styles.checkboxLabel}>
                    I accept the{' '}
                    <Text style={styles.link}>Privacy Policy</Text>
                  </Text>
                </View>
              </View>

              {/* Register Button */}
              <Button
                mode="contained"
                onPress={handleRegister}
                loading={isLoading}
                disabled={isLoading}
                style={styles.registerButton}
                contentStyle={styles.buttonContent}
                labelStyle={styles.buttonLabel}>
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>

              {/* Sign In Link */}
              <View style={styles.signInContainer}>
                <Text style={styles.signInText}>Already have an account? </Text>
                <TouchableOpacity onPress={handleSignIn}>
                  <Text style={styles.signInLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Security Notice */}
        <Card style={styles.securityCard}>
          <Card.Content>
            <View style={styles.securityContent}>
              <Icon name="security" size={24} color={colors.success} />
              <View style={styles.securityText}>
                <Text style={styles.securityTitle}>
                  Secure & Encrypted
                </Text>
                <Text style={styles.securityDescription}>
                  Your personal and financial information is protected with bank-level security encryption.
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Snackbar for messages */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        style={styles.snackbar}>
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    marginTop: spacing.lg,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  card: {
    elevation: 4,
    borderRadius: 12,
    marginBottom: spacing.lg,
  },
  form: {
    paddingVertical: spacing.md,
  },
  input: {
    marginBottom: spacing.md,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  checkboxText: {
    flex: 1,
    marginLeft: spacing.sm,
    marginTop: spacing.xs,
  },
  checkboxLabel: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  link: {
    color: colors.primary,
    fontWeight: '500',
  },
  registerButton: {
    borderRadius: 8,
    marginTop: spacing.lg,
    marginBottom: spacing.lg,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  signInLink: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  securityCard: {
    elevation: 2,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  securityContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  securityText: {
    flex: 1,
    marginLeft: spacing.md,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: spacing.xs,
  },
  securityDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  snackbar: {
    backgroundColor: colors.error,
  },
});

export default RegisterScreen;