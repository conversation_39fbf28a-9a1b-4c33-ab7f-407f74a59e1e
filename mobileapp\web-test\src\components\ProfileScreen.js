import React from 'react';

const ProfileScreen = ({ user, onSignOut }) => {
  const handleSignOut = () => {
    if (window.confirm('Are you sure you want to sign out?')) {
      onSignOut();
    }
  };

  const handleMenuClick = (item) => {
    alert(`${item} feature would be implemented in the full mobile app.`);
  };

  return (
    <div className="screen-container">
      <h2 style={{ margin: '0 0 24px 0', color: '#1F2937' }}>
        Profile & Settings
      </h2>

      {/* User Profile */}
      <div className="card">
        <div style={{ 
          display: 'flex', 
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <div style={{ 
            width: '60px', 
            height: '60px', 
            background: '#1E3A8A', 
            borderRadius: '50%', 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            marginRight: '16px'
          }}>
            {user.email.charAt(0).toUpperCase()}
          </div>
          <div>
            <div style={{ fontWeight: 'bold', color: '#1F2937', fontSize: '18px' }}>
              {user.user_metadata?.full_name || user.email.split('@')[0]}
            </div>
            <div style={{ color: '#6B7280', fontSize: '14px' }}>
              {user.email}
            </div>
            <div style={{ color: '#1E3A8A', fontSize: '12px' }}>
              Member since {new Date(user.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>

      {/* Account Management */}
      <div className="card">
        <div className="card-header">Account Management</div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <button 
            className="btn btn-secondary"
            onClick={() => handleMenuClick('Edit Profile')}
            style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}
          >
            <span style={{ marginRight: '8px' }}>✏️</span>
            Edit Profile
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={() => handleMenuClick('Security Settings')}
            style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}
          >
            <span style={{ marginRight: '8px' }}>🔒</span>
            Security Settings
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={() => handleMenuClick('KYC Verification')}
            style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}
          >
            <span style={{ marginRight: '8px' }}>✅</span>
            KYC Verification
          </button>
        </div>
      </div>

      {/* App Settings */}
      <div className="card">
        <div className="card-header">App Settings</div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '8px 0'
          }}>
            <div>
              <div style={{ fontWeight: '500', color: '#1F2937' }}>
                🔔 Push Notifications
              </div>
              <div style={{ fontSize: '12px', color: '#6B7280' }}>
                Receive share purchase updates
              </div>
            </div>
            <label style={{ position: 'relative', display: 'inline-block', width: '40px', height: '20px' }}>
              <input type="checkbox" defaultChecked style={{ opacity: 0, width: 0, height: 0 }} />
              <span style={{ 
                position: 'absolute',
                cursor: 'pointer',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: '#1E3A8A',
                borderRadius: '20px',
                transition: '0.4s'
              }}></span>
            </label>
          </div>
          
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '8px 0'
          }}>
            <div>
              <div style={{ fontWeight: '500', color: '#1F2937' }}>
                👆 Biometric Login
              </div>
              <div style={{ fontSize: '12px', color: '#6B7280' }}>
                Use fingerprint or face ID
              </div>
            </div>
            <label style={{ position: 'relative', display: 'inline-block', width: '40px', height: '20px' }}>
              <input type="checkbox" style={{ opacity: 0, width: 0, height: 0 }} />
              <span style={{ 
                position: 'absolute',
                cursor: 'pointer',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: '#D1D5DB',
                borderRadius: '20px',
                transition: '0.4s'
              }}></span>
            </label>
          </div>
        </div>
      </div>

      {/* Support */}
      <div className="card">
        <div className="card-header">Support & Information</div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <button 
            className="btn btn-secondary"
            onClick={() => handleMenuClick('Help & Support')}
            style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}
          >
            <span style={{ marginRight: '8px' }}>❓</span>
            Help & Support
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={() => handleMenuClick('About Aureus Alliance')}
            style={{ textAlign: 'left', display: 'flex', alignItems: 'center' }}
          >
            <span style={{ marginRight: '8px' }}>ℹ️</span>
            About Aureus Alliance
          </button>
        </div>
      </div>

      {/* App Information */}
      <div className="card" style={{ textAlign: 'center', background: '#F9FAFB' }}>
        <div style={{ fontWeight: 'bold', color: '#1E3A8A', marginBottom: '4px' }}>
          Aureus Alliance Holdings
        </div>
        <div style={{ fontSize: '12px', color: '#6B7280', marginBottom: '4px' }}>
          Version 1.0.0
        </div>
        <div style={{ fontSize: '10px', color: '#9CA3AF' }}>
          Secure gold share purchase platform
        </div>
      </div>

      {/* Sign Out */}
      <button 
        className="btn"
        onClick={handleSignOut}
        style={{ 
          background: '#FEF2F2',
          color: '#DC2626',
          border: '1px solid #FECACA'
        }}
      >
        🚪 Sign Out
      </button>

      {/* Demo Notice */}
      <div className="info-message">
        <strong>Web Testing Mode:</strong><br/>
        This profile screen demonstrates the mobile app interface. Settings changes and menu items would be fully functional in the production mobile app.
      </div>
    </div>
  );
};

export default ProfileScreen;